{"Camera": {"Area Scan": {"Genie Nano": {"GigE": {"models": ["Genie <PERSON>-1GigE", "<PERSON><PERSON>-2.5GigE", "<PERSON>ie <PERSON>-5GigE"], "interfaces": ["GigE Vision"]}, "5GigE": {"models": ["Genie Nano-5GigE C1920", "Genie Nano-5GigE C2590"], "interfaces": ["5GigE Vision"]}, "10GigE": {"models": ["Genie Nano-10GigE C4090", "Genie Nano-10GigE C4180"], "interfaces": ["10GigE Vision"]}}, "Genie Micro": {"GigE": {"models": ["Genie Micro-1GigE", "Genie Micro-2.5GigE"], "interfaces": ["GigE Vision"]}, "USB3": {"models": ["Genie Micro-USB3", "Genie Micro-USB3.1"], "interfaces": ["USB3 Vision"]}}, "Blackfly S": {"GigE": {"models": ["Blackfly S GigE", "Blackfly S PoE"], "interfaces": ["GigE Vision"]}, "USB3": {"models": ["Blackfly S USB3", "Blackfly S USB3.1"], "interfaces": ["USB3 Vision"]}}}, "Line Scan": {"Linea": {"GigE": {"Monochrome": {"models": ["LA-GM-02K08A", "LA-GM-04K08A", "LA-GM-08K08A"]}, "Color": {"models": ["LA-GC-02K08A", "LA-GC-04K08A"]}}, "Camera Link": {"Monochrome": {"models": ["LA-CM-02K08A", "LA-CM-04K08A", "LA-CM-08K08A", "LA-CM-16K05A"]}, "Color": {"models": ["LA-CC-04K05B", "LA-CC-08K05B"]}}}, "Piranha": {"GigE": {"models": ["Piranha4 GigE", "Piranha4 Color GigE"], "interfaces": ["GigE Vision"]}, "Camera Link": {"models": ["Piranha4 CL", "Piranha4 Color CL"], "interfaces": ["Camera Link"]}}}}, "Software": {"Sapera LT": {"Sapera GigE": {"versions": ["8.70", "8.80", "8.90"], "platforms": ["Windows", "Linux"]}, "Sapera USB": {"versions": ["8.70", "8.80", "8.90"], "platforms": ["Windows", "Linux"]}, "Sapera Camera Link": {"versions": ["8.70", "8.80", "8.90"], "platforms": ["Windows", "Linux"]}}, "Spinnaker SDK": {"Spinnaker C++": {"versions": ["2.7", "3.0", "3.1"], "platforms": ["Windows", "Linux", "macOS"]}, "Spinnaker Python": {"versions": ["2.7", "3.0", "3.1"], "platforms": ["Windows", "Linux", "macOS"]}, "Spinnaker .NET": {"versions": ["2.7", "3.0", "3.1"], "platforms": ["Windows"]}}, "eBUS SDK": {"eBUS Player": {"versions": ["6.1", "6.2", "6.3"], "platforms": ["Windows", "Linux"]}, "eBUS SDK C++": {"versions": ["6.1", "6.2", "6.3"], "platforms": ["Windows", "Linux"]}}}, "Frame Grabber": {"Xcelera": {"Xcelera-CL PX4": {"interfaces": ["Camera Link Base", "Camera Link Medium", "Camera Link Full"], "models": ["Xcelera-CL PX4 Express", "Xcelera-CL PX4 PCIe"]}, "Xcelera-CL PX8": {"interfaces": ["Camera Link Base", "Camera Link Medium", "Camera Link Full"], "models": ["Xcelera-CL PX8 Express", "Xcelera-CL PX8 PCIe"]}}, "Radient": {"Radient eV-CL": {"interfaces": ["Camera Link Base", "Camera Link Medium"], "models": ["Radient eV-CL", "Radient eV-CL Dual"]}, "Radient eV-CXP": {"interfaces": ["CoaXPress CXP-6", "CoaXPress CXP-12"], "models": ["Radient eV-CXP", "Radient eV-CXP Quad"]}}}, "Accessories": {"Cables": {"GigE Cables": {"types": ["Cat5e", "Cat6", "Cat6a"], "lengths": ["1m", "3m", "5m", "10m", "15m", "30m"]}, "Camera Link Cables": {"types": ["SDR", "MDR"], "lengths": ["1m", "3m", "5m", "10m"]}, "USB Cables": {"types": ["USB3.0", "USB3.1", "USB-C"], "lengths": ["1m", "3m", "5m"]}}, "Lenses": {"C-Mount": {"focal_lengths": ["8mm", "12mm", "16mm", "25mm", "35mm", "50mm"], "types": ["Fixed", "Varifocal", "Zoom"]}, "CS-Mount": {"focal_lengths": ["2.8mm", "4mm", "6mm", "8mm", "12mm"], "types": ["Fixed", "Varifocal"]}}, "Lighting": {"LED Lights": {"types": ["Ring Light", "Bar Light", "Dome Light", "Spot Light"], "colors": ["White", "Red", "Blue", "Green", "IR"]}, "Strobe Lights": {"types": ["High Power Strobe", "Compact Strobe"], "colors": ["White", "Red", "Blue"]}}}}