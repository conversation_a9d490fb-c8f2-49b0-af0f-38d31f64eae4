import uuid
import mysql.connector
from typing import List
import weaviate
import openai
import argparse
from datetime import datetime

# --- Configuration ---
WEAVIATE_URL = "http://localhost:8080"
SOFTWARE_CHUNK_CLASS = "SoftwareChunkV1"  # Software documentation class
EMBEDDING_BATCH_SIZE = 16  # process multiple at once

# OpenAI & Weaviate
openai.api_key = "********************************************************************************************************************************************************************"  # replace with env/config/ENV VAR
client_weaviate = weaviate.Client(WEAVIATE_URL)

# MySQL DB config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough1'
}

# ---------- Weaviate Schema Setup ----------

def setup_software_weaviate_schema(client):
    """Create SoftwareChunkV1 class if it doesn't exist."""
    try:
        schema = client.schema.get()
        existing_classes = [cls['class'] for cls in schema.get('classes', [])]

        print(f"🔍 Existing classes: {existing_classes}")

        if SOFTWARE_CHUNK_CLASS not in existing_classes:
            software_class = {
                "class": SOFTWARE_CHUNK_CLASS,
                "description": "Software documentation chunks with software-specific metadata",
                "properties": [
                    {"name": "source_file", "dataType": ["text"], "description": "Filename of software document"},
                    {"name": "chunk_number", "dataType": ["int"], "description": "Index of the chunk"},
                    {"name": "content", "dataType": ["text"], "description": "Actual chunk text"},
                    {"name": "software_name", "dataType": ["text"], "description": "Software name (e.g., 'Sapera LT', 'Spinnaker')"},
                    {"name": "software_version", "dataType": ["text"], "description": "Software version (e.g., 'v9.0', 'v4.2')"},
                    {"name": "api_type", "dataType": ["text"], "description": "API type (e.g., 'C++', '.NET', 'C', 'Python')"},
                    {"name": "module_name", "dataType": ["text"], "description": "Module or feature name"},
                    {"name": "document_type", "dataType": ["text"], "description": "Type of document (e.g., 'API Reference', 'User Guide', 'Tutorial')"},
                    {"name": "created_at", "dataType": ["text"], "description": "Timestamp of ingestion"}
                ],
                "vectorizer": "none"
            }

            print(f"🔧 Creating {SOFTWARE_CHUNK_CLASS} class...")
            client.schema.create_class(software_class)
            print(f"✅ Created {SOFTWARE_CHUNK_CLASS} class")
        else:
            print(f"ℹ️ {SOFTWARE_CHUNK_CLASS} class already exists")

    except Exception as e:
        print(f"❌ Error setting up software schema: {e}")
        import traceback
        traceback.print_exc()

# ---------- Database Helpers ----------

def get_software_chunks_from_db():
    """Fetch software chunks that haven't been vectorized yet."""
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)
    
    # Only get software documents (camera_type = 'software')
    cursor.execute("""
        SELECT id, source_file, chunk_number, content, file_hash, 
               last_modified, created_at, camera_type
        FROM pdf_chunks 
        WHERE vector_embedded = 0 AND camera_type = 'software'
        ORDER BY source_file, chunk_number
    """)
    
    chunks = cursor.fetchall()
    cursor.close()
    conn.close()
    
    print(f"📊 Found {len(chunks)} software chunks to vectorize")
    return chunks

def update_software_vectorized_flag(chunk_ids: List[int]):
    """Mark software chunks as vectorized in DB."""
    if not chunk_ids:
        return
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    format_ids = ",".join(["%s"] * len(chunk_ids))
    cursor.execute(f"UPDATE pdf_chunks SET vector_embedded = 1 WHERE id IN ({format_ids})", chunk_ids)
    conn.commit()
    cursor.close()
    conn.close()

# ---------- Embedding Helpers ----------

def get_embeddings_batch(texts: List[str]) -> List[List[float]]:
    """Fetch embeddings from OpenAI API for a batch of texts."""
    response = openai.Embedding.create(
        input=texts,
        model="text-embedding-3-large"
    )
    return [item.embedding for item in response.data]

def extract_software_metadata(source_file, content):
    """Extract software-specific metadata from filename and content."""
    filename_lower = source_file.lower()
    content_lower = content.lower()
    
    # Extract software name from filename
    software_name = "Unknown"
    if "sapera" in filename_lower:
        software_name = "Sapera LT"
    elif "spinnaker" in filename_lower:
        software_name = "Spinnaker"
    elif "pylon" in filename_lower:
        software_name = "Pylon"
    elif "visionpoint" in filename_lower:
        software_name = "VisionPoint"
    
    # Extract API type from content
    api_type = ""
    if any(keyword in content_lower for keyword in ["c++", "cpp", "class", "namespace"]):
        api_type = "C++"
    elif any(keyword in content_lower for keyword in [".net", "c#", "csharp", "using system"]):
        api_type = ".NET"
    elif any(keyword in content_lower for keyword in ["python", "import", "def ", "class "]):
        api_type = "Python"
    elif any(keyword in content_lower for keyword in ["java", "public class", "import java"]):
        api_type = "Java"
    
    # Extract document type
    document_type = "User Guide"
    if any(keyword in content_lower for keyword in ["api reference", "function", "method", "parameter"]):
        document_type = "API Reference"
    elif any(keyword in content_lower for keyword in ["tutorial", "example", "step by step"]):
        document_type = "Tutorial"
    elif any(keyword in content_lower for keyword in ["installation", "setup", "getting started"]):
        document_type = "Installation Guide"
    
    # Extract version (basic pattern matching)
    software_version = ""
    import re
    version_match = re.search(r'v?(\d+\.\d+(?:\.\d+)?)', content)
    if version_match:
        software_version = f"v{version_match.group(1)}"
    
    return {
        "software_name": software_name,
        "software_version": software_version,
        "api_type": api_type,
        "document_type": document_type,
        "module_name": ""  # Can be enhanced later
    }

# ---------- Main Vectorization Logic ----------

def store_software_embeddings(chunks):
    """Store software chunks with embeddings in Weaviate."""
    if not chunks:
        print("No software chunks to process.")
        return
    
    successful_ids = []
    
    # Process in batches
    for i in range(0, len(chunks), EMBEDDING_BATCH_SIZE):
        batch = chunks[i:i + EMBEDDING_BATCH_SIZE]
        texts = [chunk["content"] for chunk in batch]
        
        try:
            embeddings = get_embeddings_batch(texts)
            
            for chunk, embedding in zip(batch, embeddings):
                try:
                    obj_uuid = str(uuid.uuid4())
                    
                    # Extract software-specific metadata
                    metadata = extract_software_metadata(chunk["source_file"], chunk["content"])
                    
                    # Prepare created_at timestamp as string
                    created_at_value = chunk.get("created_at")
                    if isinstance(created_at_value, str):
                        created_at_value = created_at_value
                    elif created_at_value is None:
                        created_at_value = datetime.now().isoformat()
                    else:
                        created_at_value = str(created_at_value)
                    
                    data_object = {
                        "source_file": str(chunk["source_file"]),
                        "chunk_number": int(chunk["chunk_number"]),
                        "content": str(chunk["content"]),
                        "software_name": str(metadata["software_name"]),
                        "software_version": str(metadata["software_version"]),
                        "api_type": str(metadata["api_type"]),
                        "module_name": str(metadata["module_name"]),
                        "document_type": str(metadata["document_type"]),
                        "created_at": str(created_at_value)
                    }
                    
                    client_weaviate.data_object.create(
                        data_object=data_object,
                        class_name=SOFTWARE_CHUNK_CLASS,
                        vector=embedding,
                        uuid=obj_uuid
                    )
                    
                    successful_ids.append(chunk["id"])
                    print(f"✅ Vectorized software chunk {chunk['chunk_number']} from {chunk['source_file']} → {SOFTWARE_CHUNK_CLASS}")
                except Exception as e:
                    print(f"❌ Failed to vectorize software chunk: {e}")
                    continue
                    
        except Exception as e:
            print(f"❌ Failed to get embeddings for batch: {e}")
            continue
    
    update_software_vectorized_flag(successful_ids)

# ---------- Main Entry ----------

def main(args):
    if args.weaviate:
        setup_software_weaviate_schema(client_weaviate)
        chunks = get_software_chunks_from_db()
        if not chunks:
            print("No software chunks to vectorize.")
            return
        
        store_software_embeddings(chunks)
        print("🎉 Software vectorization complete.")
    else:
        print("Use --weaviate to start software embedding.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--weaviate", action="store_true", help="Use Weaviate for software vector storage")
    args = parser.parse_args()
    main(args)
