import uuid
import mysql.connector
from typing import List
import weaviate
import openai
import argparse
import sys, io

# --- Encoding Fix for Windows ---
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# --- Configuration ---
WEAVIATE_URL = "http://localhost:8080"
EMBEDDING_BATCH_SIZE = 16  # process multiple at once

# OpenAI & Weaviate
openai.api_key = "********************************************************************************************************************************************************************"
client_weaviate = weaviate.Client(WEAVIATE_URL)

# MySQL DB config
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough1'
}

# ---------- Camera Type → Weaviate Class Map ----------
CAMERA_CLASS_MAP = {
    "linea_gige_color": "LineaGiGEColor",
    "linea_gige_mono": "LineaGiGEMono",
    "linea_cameralink_color": "LineaCameraLinkColor",
    "linea_cameralink_mono": "LineaCameraLinkMono",
}

# ---------- DB Helpers ----------

def get_chunks_from_db() -> List[dict]:
    """Fetch unvectorized chunks from DB (Hybrid chunking already inserted)."""
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor(dictionary=True)
    cursor.execute("""
        SELECT id, source_file, chunk_number, content, camera_type,
               page_number, section_title, created_at
        FROM pdf_chunks
        WHERE vector_embedded = 0
    """)
    rows = cursor.fetchall()
    cursor.close()
    conn.close()
    return rows

def update_vectorized_flag(chunk_ids: List[int]):
    """Mark chunks as vectorized in DB."""
    if not chunk_ids:
        return
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    format_ids = ",".join(["%s"] * len(chunk_ids))
    cursor.execute(f"UPDATE pdf_chunks SET vector_embedded = 1 WHERE id IN ({format_ids})", chunk_ids)
    conn.commit()
    cursor.close()
    conn.close()

# ---------- Embedding Helpers ----------

def get_embeddings_batch(texts: List[str]) -> List[List[float]]:
    """Fetch embeddings from OpenAI API for a batch of texts."""
    response = openai.Embedding.create(
        input=texts,
        model="text-embedding-3-large"
    )
    return [item.embedding for item in response.data]

def batch_chunks(chunks: List[dict], batch_size: int):
    """Yield batches of chunks for efficient processing."""
    for i in range(0, len(chunks), batch_size):
        yield chunks[i:i + batch_size]

# ---------- Schema Setup ----------

def ensure_class_exists(client: weaviate.Client, class_name: str):
    """Create class in Weaviate if missing."""
    schema = client.schema.get()
    existing_classes_lower = [c["class"].lower() for c in schema.get("classes", [])]

    if class_name.lower() not in existing_classes_lower:
        new_class = {
            "class": class_name,
            "description": f"Document chunks for {class_name} camera manuals",
            "properties": [
                {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
                {"name": "chunk_number", "dataType": ["int"], "description": "Index of the chunk"},
                {"name": "content", "dataType": ["text"], "description": "Actual chunk text"},
                {"name": "camera_type", "dataType": ["string"], "description": "Camera type"},
                {"name": "page_number", "dataType": ["int"], "description": "Page number from source PDF"},
                {"name": "section_title", "dataType": ["string"], "description": "Section heading/title"},
                {"name": "created_at", "dataType": ["string"], "description": "Insertion timestamp"}
            ],
            "vectorizer": "none"
        }
        client.schema.create_class(new_class)
        print(f"Created class '{class_name}'.")

# ---------- Logic ----------

def stable_uuid(chunk: dict) -> str:
    """Stable UUID from source_file + chunk_number."""
    base = f"{chunk['source_file']}_{chunk['chunk_number']}"
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, base))

def store_embeddings(chunks: List[dict]):
    """Generate embeddings for chunks and push to Weaviate."""
    print(f"Processing {len(chunks)} chunks in batches of {EMBEDDING_BATCH_SIZE}...")

    for batch in batch_chunks(chunks, EMBEDDING_BATCH_SIZE):
        texts = [chunk["content"] for chunk in batch if chunk.get("content")]
        if not texts:
            continue

        try:
            embeddings = get_embeddings_batch(texts)
        except Exception as e:
            print(f"[ERROR] Embedding failed for batch: {e}")
            continue

        successful_ids = []
        for idx, chunk in enumerate(batch):
            try:
                embedding = embeddings[idx]
                obj_uuid = stable_uuid(chunk)
                camera_type = chunk.get("camera_type") or "unknown"
                class_name = CAMERA_CLASS_MAP.get(camera_type, "LineaGiGEColor")

                ensure_class_exists(client_weaviate, class_name)

                # Skip if already exists
                if client_weaviate.data_object.exists(uuid=obj_uuid, class_name=class_name):
                    print(f"[SKIP] Already exists: {chunk['chunk_number']} from {chunk['source_file']}")
                    continue

                created_at_value = chunk.get("created_at")
                if created_at_value and not isinstance(created_at_value, str):
                    created_at_value = created_at_value.replace(microsecond=0).isoformat() + "Z"
                elif not created_at_value:
                    created_at_value = ""

                data_object = {
                    "source_file": chunk["source_file"],
                    "chunk_number": int(chunk["chunk_number"]),
                    "content": chunk["content"],
                    "camera_type": camera_type,
                    "page_number": chunk.get("page_number"),
                    "section_title": chunk.get("section_title") or "Unknown",
                    "created_at": created_at_value
                }

                client_weaviate.data_object.create(
                    data_object=data_object,
                    class_name=class_name,
                    vector=embedding,
                    uuid=obj_uuid
                )

                successful_ids.append(chunk["id"])
                print(f"[OK] Vectorized chunk {chunk['chunk_number']} from {chunk['source_file']} → {class_name}")
            except Exception as e:
                print(f"[ERROR] Failed to vectorize chunk: {e}")
                continue

        update_vectorized_flag(successful_ids)

# ---------- Main Entry ----------

def main(args):
    if args.weaviate:
        chunks = get_chunks_from_db()
        if not chunks:
            print("No chunks to vectorize.")
            return
        store_embeddings(chunks)
        print("🎉 Vectorization complete.")
    else:
        print("Use --weaviate to start embedding.")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--weaviate", action="store_true", help="Use Weaviate for vector storage")
    args = parser.parse_args()
    main(args)
