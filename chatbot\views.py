from django.http import JsonResponse, HttpResponse, Http404
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.db.models import Q
from urllib.parse import unquote
from datetime import datetime
import weaviate
#from openai import OpenAI
from .models import PdfFile, SupportTicket, PromptTemplate, OpenAIUsage, ChatHistory, DynamicFlow
from . import models
from .upload import upload_pdf_to_db
import logging


# chatbot/views.py

import json
import os
from django.http import JsonResponse, HttpResponseForbidden, HttpResponseBadRequest
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.views import View



# views.py
# chatbot/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .models import PromptTemplate
from .serializers import PromptTemplateSerializer



import json
import os
from django.conf import settings
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import datetime
from .models import SupportTicket
from scipy.spatial.distance import cosine
from django.utils import timezone
from datetime import timedelta
from .models import SupportTicket  # Make sure this import exists
import openai
from .openai_usage_tracker import track_openai_chat_completion




AUTO_CLOSE_DAYS = 7  # days after which tickets auto-close

def auto_close_expired_tickets():
    """Close tickets older than AUTO_CLOSE_DAYS (default: 7 days)."""
    cutoff = timezone.now() - timedelta(days=AUTO_CLOSE_DAYS)
    expired = SupportTicket.objects.filter(status="open", created_at__lt=cutoff)
    count = expired.update(status="closed")
    if count:
        print(f"✅ Auto-closed {count} expired ticket(s).")

# --- pending tickets -------------------------------------------------

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def open_tickets(request):
    """
    Return all OPEN tickets for the logged-in user,
    each with a one-line 'summary' (added in serializer earlier).
    """
    auto_close_expired_tickets()          # ⏱ keep DB tidy

    tickets = SupportTicket.objects.filter(
        user=request.user,
        status="open"
    ).order_by('-last_activity')          # newest first

    serializer = SupportTicketSerializer(tickets, many=True)
    return Response(serializer.data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ticket_detail(request, ticket_number):
    """
    Fetch one ticket (only if it belongs to this user).
    Front-end uses this when the user clicks a ticket to resume it.
    """
    auto_close_expired_tickets()

    ticket = get_object_or_404(
        SupportTicket,
        user=request.user,
        ticket_number=ticket_number
    )
    ser = SupportTicketSerializer(ticket)
    return Response(ser.data)


# --- Pending-ticket list ---------------------------------------
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SupportTicket

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_tickets(request):
    tickets = []
    user = request.user

    open_tickets = SupportTicket.objects.filter(user=user, status="open").order_by('-last_activity')
    for t in open_tickets:
        desc_lines = (t.problem_description or "").splitlines()
        issue = desc_lines[0][:60] if desc_lines else "No description"

        tickets.append({
            "ticket_number": t.ticket_number,
            "status": t.status,
            "issue": issue,
            "title": t.short_title or f"{t.product_type} - {t.model_number}" if t.product_type and t.model_number else "No title",
            "short_title": t.short_title or "No title",
            "problem_description": t.problem_description or "No description available",
            "product_type": t.product_type,
            "model": t.model_number,
            "created_at": t.created_at,
            "last_activity": t.last_activity,
        })

    return Response({"tickets": tickets})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def closed_tickets(request):
    """
    Return all CLOSED tickets for the logged-in user.
    """
    tickets = []
    user = request.user

    closed_tickets_qs = SupportTicket.objects.filter(user=user, status="closed").order_by('-last_activity')
    for t in closed_tickets_qs:
        desc_lines = (t.problem_description or "").splitlines()
        issue = desc_lines[0][:60] if desc_lines else "No description"

        tickets.append({
            "ticket_number": t.ticket_number,
            "status": t.status,
            "issue": issue,
            "title": t.short_title or f"{t.product_type} - {t.model_number}" if t.product_type and t.model_number else "No title",
            "short_title": t.short_title or "No title",
            "problem_description": t.problem_description or "No description available",
            "solution_summary": t.solution_summary or "No solution summary available",
            "product_type": t.product_type,
            "model": t.model_number,
            "created_at": t.created_at,
            "last_activity": t.last_activity,
        })

    return Response({"tickets": tickets})




from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from .models import SupportTicket
from .serializers import SupportTicketSerializer

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def ticket_summary(request):
    """
    Return a summary of the ticket's problem, solution, and status.
    """
    ticket_number = request.data.get("ticket_number")
    if not ticket_number:
        return Response({"error": "Ticket number is required."}, status=status.HTTP_400_BAD_REQUEST)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=status.HTTP_404_NOT_FOUND)

    serializer = SupportTicketSerializer(ticket)
    return Response(serializer.data)

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.utils.timezone import now
from datetime import timedelta
from chatbot.models import SupportTicket

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def pending_ticket_summaries(request):
    """
    Return list of user's open tickets created within last 7 days,
    each with ticket_number and short_title.
    """
    seven_days_ago = now() - timedelta(days=7)

    tickets = (
        SupportTicket.objects
        .filter(user=request.user, status="open", created_at__gte=seven_days_ago)
        .order_by('-created_at')
        .values('ticket_number', 'short_title')
    )

    ticket_list = [
        {
            "ticket_number": t['ticket_number'],
            "short_title": t['short_title'] if t['short_title'] else "No title"
        }
        for t in tickets
    ]

    if not ticket_list:
        return Response({"message": "No pending tickets found."})

    return Response(ticket_list)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def start_ticket_session(request, ticket_number):
    """
    Start a chatbot session with a specific ticket.
    Returns ticket details and initial message for the chatbot.
    """
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    if ticket.status == "closed":
        return Response({"error": "Cannot start session with a closed ticket."}, status=400)

    serializer = SupportTicketSerializer(ticket)

    # Prepare initial message based on ticket state
    if ticket.problem_description:
        # Get user's name
        username = ticket.user.name if hasattr(ticket.user, 'name') and ticket.user.name else ticket.user.official_email.split('@')[0]
        categories = ", ".join(ticket.problem_categories) if ticket.problem_categories else "general issues"
        initial_message = f'Welcome "{username}". Your ticket "{ticket.ticket_number}" has been raised. Please explain the problem related to the "{categories}".'
        awaiting_description = False
    else:
        # Get user's name for new tickets
        username = ticket.user.name if hasattr(ticket.user, 'name') and ticket.user.name else ticket.user.official_email.split('@')[0]
        categories = ", ".join(ticket.problem_categories) if ticket.problem_categories else "general issues"
        initial_message = f'Welcome "{username}". Your ticket "{ticket.ticket_number}" has been raised. Please explain the problem related to the "{categories}".'
        awaiting_description = True

    return Response({
        "ticket": serializer.data,
        "initial_message": initial_message,
        "session_type": "ticket",
        "awaiting_description": awaiting_description
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def start_general_session(request):
    """
    Start a general chatbot session without ticket context.
    """
    return Response({
        "initial_message": "Hello! I'm here to help with general questions about our products and services. How can I assist you today?",
        "session_type": "general",
        "ticket": None
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def end_session(request):
    """
    End the current session and optionally close ticket.
    This can be used for cleanup when user logs out or session ends.
    """
    ticket_number = request.data.get("ticket_number")
    close_ticket = request.data.get("close_ticket", False)

    if ticket_number and close_ticket:
        try:
            ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
            if ticket.status == "open":
                ticket.status = "closed"
                ticket.save(update_fields=["status"])
                return Response({"message": f"Session ended and ticket {ticket_number} closed."})
        except SupportTicket.DoesNotExist:
            pass

    return Response({"message": "Session ended successfully."})


PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"

class PromptTemplateView(APIView):
    permission_classes = [AllowAny]  # Allow unauthenticated access for prompt templates

    def get(self, request):
        prompt_type = request.query_params.get("type")
        if not prompt_type:
            return Response({"error": "Prompt type is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with open(PROMPTS_JSON_PATH, "r") as f:
                prompts = json.load(f)
        except FileNotFoundError:
            return Response({"error": "Prompts file not found"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        except json.JSONDecodeError:
            return Response({"error": "Prompts file is invalid JSON"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        prompt = prompts.get(prompt_type)
        if not prompt or not prompt.get("is_active"):
            return Response({"error": "No active prompt found"}, status=status.HTTP_404_NOT_FOUND)

        return Response({
            "name": prompt_type,
            "template": prompt.get("template"),
            "is_active": prompt.get("is_active"),
            "last_modified": prompt.get("last_modified"),
        })

    def post(self, request):
        data = request.data
        prompt_type = data.get("prompt_type")
        template = data.get("template")
        is_active = data.get("is_active", True)

        if not prompt_type or not template:
            return Response({"error": "prompt_type and template are required"}, status=status.HTTP_400_BAD_REQUEST)

        # Read current prompts from JSON
        try:
            with open(PROMPTS_JSON_PATH, "r") as f:
                prompts = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            prompts = {}

        # Deactivate all other prompts of the same type
        for key in prompts:
            if key == prompt_type:
                prompts[key]["is_active"] = False

        # Add/update the prompt, mark active per request
        prompts[prompt_type] = {
            "template": template,
            "is_active": is_active,
            "last_modified": datetime.utcnow().isoformat(),
        }

        # Save back to JSON file
        try:
            with open(PROMPTS_JSON_PATH, "w") as f:
                json.dump(prompts, f, indent=2)
        except Exception as e:
            return Response({"error": f"Failed to save prompts: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            "name": prompt_type,
            "template": template,
            "is_active": is_active,
            "last_modified": prompts[prompt_type]["last_modified"],
        }, status=status.HTTP_201_CREATED)




@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_organization(request):
    user = request.user
    input_org = request.data.get("organization", "").strip().lower()
    actual_org = user.organization.strip().lower() if user.organization else ""

    if input_org == actual_org:
        return Response({"status": "verified", "message": "✅ Organization matched. You are verified."})
    else:
        return Response({"status": "not_verified", "message": "❌ Organization mismatch. Please login with the correct account."}, status=400)



# views.py
# chatbot/views.py

def generate_gpt_summary(text, prompt_prefix, max_tokens=300):
    """
    Uses OpenAI GPT model to generate a concise summary of the given text.

    Args:
        text (str): The input text to summarize.
        prompt_prefix (str): Instruction prompt to guide summarization.
        max_tokens (int): Max tokens for the GPT response.

    Returns:
        str or None: The generated summary text or None on failure.
    """
    prompt = f"{prompt_prefix}\n\n{text}"
    try:
        response = track_openai_chat_completion(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.9,
            max_tokens=max_tokens,
            purpose="gpt_summary"
        )
        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"GPT summary error: {e}")
        return None

def generate_ticket_content(product_info):
    """
    Generate Problem Description and Short Title using GPT-4.0 mini based on product information.

    Args:
        product_info (dict): Dictionary containing product details

    Returns:
        dict: Contains 'problem_description' and 'short_title' or None on failure
    """
    try:
        # Create a comprehensive prompt for generating ticket content
        prompt = f"""
Based on the following product information, generate a professional problem description and short title for a support ticket:

Product Type: {product_info.get('product_type', 'N/A')}
Brand: {product_info.get('brand', 'N/A')}
Model Number: {product_info.get('model_number', 'N/A')}
Serial Number: {product_info.get('serial_number', 'N/A')}
Operating System: {product_info.get('operating_system_detailed', 'N/A')}
Purchased From: {product_info.get('purchased_from', 'N/A')}
Year of Purchase: {product_info.get('year_of_purchase', 'N/A')}
PO Number: {product_info.get('po_number', 'N/A')}

Please generate:
1. A short title (max 60 characters) that summarizes the potential support need
2. A problem description (2-3 sentences) that describes common issues or setup requirements for this product

Format your response as:
SHORT_TITLE: [title here]
PROBLEM_DESCRIPTION: [description here]
"""

        response = track_openai_chat_completion(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.9,
            max_tokens=500,
            purpose="ticket_creation"
        )

        content = response.choices[0].message.content.strip()

        # Parse the response
        lines = content.split('\n')
        short_title = ""
        problem_description = ""

        for line in lines:
            if line.startswith("SHORT_TITLE:"):
                short_title = line.replace("SHORT_TITLE:", "").strip()
            elif line.startswith("PROBLEM_DESCRIPTION:"):
                problem_description = line.replace("PROBLEM_DESCRIPTION:", "").strip()

        # Fallback if parsing fails
        if not short_title:
            short_title = f"{product_info.get('product_type', 'Product')} - {product_info.get('model', 'Support')}"

        if not problem_description:
            problem_description = f"Support request for {product_info.get('product_type', 'product')} {product_info.get('model', 'model')}. Customer needs assistance with setup, configuration, or troubleshooting."

        return {
            'short_title': short_title[:120],  # Ensure it fits the field limit
            'problem_description': problem_description
        }

    except Exception as e:
        print(f"GPT ticket content generation error: {e}")
        # Return fallback content
        return {
            'short_title': f"{product_info.get('brand', 'Product')} {product_info.get('product_type', 'Support')} - {product_info.get('model_number', 'Support')}",
            'problem_description': f"Support request for {product_info.get('brand', 'product')} {product_info.get('product_type', 'product')} {product_info.get('model_number', 'model')}. Customer needs assistance with setup, configuration, or troubleshooting."
        }
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_info(request):
    user = request.user
    print("Logged-in user:", request.user)
    print("Name:", user.name)

    return Response({
        "name": user.name,  # ✅ This should match your DB column
        "email": user.official_email,
        "organization": user.organization
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_product_hierarchy(request):
    """
    Return the complete product hierarchy for hierarchical selection.
    """
    import os

    hierarchy_file_path = os.path.join(os.path.dirname(__file__), 'product_hierarchy.json')

    try:
        with open(hierarchy_file_path, 'r', encoding='utf-8') as f:
            hierarchy_data = json.load(f)

        return Response({
            "status": "success",
            "hierarchy": hierarchy_data
        })
    except FileNotFoundError:
        return Response({
            "status": "error",
            "message": "Product hierarchy file not found"
        }, status=404)
    except json.JSONDecodeError:
        return Response({
            "status": "error",
            "message": "Invalid product hierarchy data"
        }, status=500)
    except Exception as e:
        return Response({
            "status": "error",
            "message": f"Error loading product hierarchy: {str(e)}"
        }, status=500)

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .serializers import SupportTicketSerializer

 # import your GPT helper function here

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_ticket(request):
    # DEBUG: Log the incoming request data
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"=== CREATE TICKET REQUEST DATA ===")
    logger.error(f"Request data: {request.data}")

    # Extract product information from request
    # Map 'software' field to 'sdk_software_used' for backward compatibility
    software_value = request.data.get('software', '') or request.data.get('sdk_software_used', '')

    # Parse software field to extract name and version
    # Format: "Sapera LT (v9.0)" or "Spinnaker (v4.2)"
    sdk_software_name = ''
    sdk_version_value = request.data.get('sdk_version', '')

    if software_value:
        if '(' in software_value and ')' in software_value:
            # Extract software name and version from combined field
            parts = software_value.split('(')
            sdk_software_name = parts[0].strip()
            version_part = parts[1].replace(')', '').strip()
            if version_part and not sdk_version_value:
                sdk_version_value = version_part
        else:
            sdk_software_name = software_value

    product_info = {
        'product_type': request.data.get('product_type', ''),
        'brand': request.data.get('brand', ''),
        'sensor_type': request.data.get('sensor_type', ''),
        'interface_type': request.data.get('interface_type', ''),
        'color_type': request.data.get('color_type', ''),
        'family_name': request.data.get('family_name', ''),
        'model_number': request.data.get('model_number', ''),
        'serial_number': request.data.get('serial_number', ''),
        'framegrabber': request.data.get('framegrabber', ''),
        'sdk_software_used': sdk_software_name,
        'sdk_version': sdk_version_value,
        'programming_language': request.data.get('programming_language', ''),
        'camera_configuration_tool': request.data.get('camera_configuration_tool', ''),
        'operating_system_detailed': request.data.get('operating_system_detailed', ''),
        'purchased_from': request.data.get('purchased_from', ''),
        'year_of_purchase': request.data.get('year_of_purchase', ''),
        'po_number': request.data.get('po_number', ''),
        # Hierarchical product selection fields
        'product_hierarchy_path': request.data.get('product_hierarchy_path', []),
        'product_category': request.data.get('product_category', ''),
        'product_subcategory': request.data.get('product_subcategory', ''),
        'product_family': request.data.get('product_family', ''),
        'product_interface': request.data.get('product_interface', ''),
    }

    # ✅ Infer camera_type from product hierarchy if not explicitly provided
    product_hierarchy_path = request.data.get('product_hierarchy_path', [])
    camera_type = request.data.get('camera_type') or infer_camera_type_from_hierarchy(product_hierarchy_path)

    if camera_type:
        print(f"📘 Inferred camera_type: {camera_type} from hierarchy: {product_hierarchy_path}")
    else:
        print(f"⚠️ No camera_type could be inferred from hierarchy: {product_hierarchy_path}")

    # Generate Problem Description and Short Title using GPT-4.0 mini
    generated_content = generate_ticket_content(product_info)

    # Add generated content to the request data
    ticket_data = request.data.copy()
    ticket_data['problem_description'] = generated_content['problem_description']
    ticket_data['short_title'] = generated_content['short_title']

    # Ensure hierarchical fields are included
    ticket_data['product_hierarchy_path'] = product_info['product_hierarchy_path']
    ticket_data['product_category'] = product_info['product_category']
    ticket_data['product_subcategory'] = product_info['product_subcategory']
    ticket_data['product_family'] = product_info['product_family']
    ticket_data['product_interface'] = product_info['product_interface']

    # Ensure SDK software fields are properly mapped
    ticket_data['sdk_software_used'] = product_info['sdk_software_used']
    ticket_data['sdk_version'] = product_info['sdk_version']

    # ✅ Store inferred camera_type in ticket_data if available
    if camera_type:
        ticket_data['camera_type'] = camera_type

    serializer = SupportTicketSerializer(data=ticket_data)

    if serializer.is_valid():
        # Save ticket with AI-generated content
        ticket = serializer.save(user=request.user)

        # Generate problem summary from the AI-generated problem description
        problem_summary = generate_gpt_summary(
            ticket.problem_description,
            "Summarize this problem description clearly and professionally:"
        )
        if problem_summary:
            ticket.problem_summary = problem_summary
            ticket.save(update_fields=["problem_summary"])

        return Response({
            "status": "success",
            "message": "Ticket created successfully.",
            "ticket_number": ticket.ticket_number,
            "generated_content": {
                "short_title": ticket.short_title,
                "problem_description": ticket.problem_description
            }
        })
    else:
        # DEBUG: Log serializer errors
        logger.error(f"=== SERIALIZER VALIDATION ERRORS ===")
        logger.error(f"Errors: {serializer.errors}")
        logger.error(f"Ticket data: {ticket_data}")
        return Response({"status": "error", "errors": serializer.errors}, status=400)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_categories(request):
    """
    Update problem categories for a ticket.
    """
    ticket_number = request.data.get('ticket_number')
    problem_categories = request.data.get('problem_categories', [])

    if not ticket_number:
        return Response({"status": "error", "message": "Ticket number is required."}, status=400)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"status": "error", "message": "Ticket not found."}, status=404)

    # Update problem categories
    ticket.problem_categories = problem_categories
    ticket.save(update_fields=['problem_categories'])

    return Response({
        "status": "success",
        "message": "Problem categories updated successfully.",
        "ticket_number": ticket.ticket_number,
        "problem_categories": ticket.problem_categories
    })



@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_description(request):
    """
    Update problem description for a ticket and re-run Weaviate + GPT prompt generation.
    ✅ FIXED: Comprehensive camera_type inference with detailed debugging.
    """
    ticket_number = request.data.get('ticket_number')
    problem_description = request.data.get('problem_description', '').strip()

    print(f"\n{'='*80}")
    print(f"🎯 UPDATE_TICKET_DESCRIPTION CALLED")
    print(f"{'='*80}")
    print(f"📋 Ticket Number: {ticket_number}")
    print(f"📝 Problem Description: {problem_description[:100]}...")

    if not ticket_number:
        return Response({"status": "error", "message": "Ticket number is required."}, status=400)
    if not problem_description:
        return Response({"status": "error", "message": "Problem description is required."}, status=400)

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
        print(f"✅ Ticket found: {ticket.ticket_number}")
    except SupportTicket.DoesNotExist:
        print(f"❌ Ticket not found: {ticket_number}")
        return Response({"status": "error", "message": "Ticket not found."}, status=404)

    # ✅ Update problem description and summary
    ticket.problem_description = problem_description
    summary = generate_gpt_summary(problem_description, "Summarize this problem clearly and professionally:")
    if summary:
        ticket.problem_summary = summary
    ticket.save(update_fields=["problem_description", "problem_summary"])
    print(f"✅ Problem description and summary updated")

    # ✅ CRITICAL: Build comprehensive product context for camera type inference
    try:
        print(f"\n{'='*80}")
        print(f"🔍 BUILDING PRODUCT CONTEXT FOR CAMERA TYPE INFERENCE")
        print(f"{'='*80}")

        # Extract all relevant fields from ticket
        product_hierarchy_path = getattr(ticket, "product_hierarchy_path", None)
        family_name = getattr(ticket, "family_name", None)
        interface_type = getattr(ticket, "interface_type", None)
        color_type = getattr(ticket, "color_type", None)
        model_number = getattr(ticket, "model_number", None)

        print(f"📊 Ticket Fields:")
        print(f"  - product_hierarchy_path: {product_hierarchy_path}")
        print(f"  - family_name: {family_name}")
        print(f"  - interface_type: {interface_type}")
        print(f"  - color_type: {color_type}")
        print(f"  - model_number: {model_number}")

        product_context = {
            "product_hierarchy_path": product_hierarchy_path,
            "familyName": family_name,
            "interfaceType": interface_type,
            "colorType": color_type,
            "modelNumber": model_number,
        }

        # ✅ Try to extract camera type from context
        camera_type = extract_camera_type_from_context(product_context)

        if camera_type:
            print(f"\n✅ SUCCESS: Camera type inferred from context")
            print(f"🎯 Final camera_type = {camera_type}")
        else:
            print(f"\n⚠️ WARNING: extract_camera_type_from_context returned None")
            print(f"⚠️ This means the ticket does not contain sufficient Linea camera information")
            print(f"⚠️ Weaviate search will be SKIPPED to prevent cross-contamination")

        # ✅ Retrieve matching document chunks from Weaviate (only if camera_type is valid)
        if camera_type:
            weaviate_class = get_weaviate_class_for_camera_type(camera_type)
            print(f"\n{'='*80}")
            print(f"🔍 WEAVIATE SEARCH CONFIGURATION")
            print(f"{'='*80}")
            print(f"🎯 Camera Type: {camera_type}")
            print(f"🔍 Weaviate Class: {weaviate_class}")
            print(f"📝 Query: {problem_description[:100]}...")

            matches = search_similar_chunks_weaviate(
                query=problem_description,
                limit=5,
                camera_type=camera_type
            )
            context_chunks = [m["content"] for m in matches] if matches else []
            print(f"✅ Retrieved {len(context_chunks)} context chunks from Weaviate")
        else:
            print(f"\n⚠️ Skipping Weaviate search (camera_type is None)")
            context_chunks = []

        # ✅ Generate and save new GPT prompt
        get_or_generate_ticket_prompt(
            ticket=ticket,
            user_description=problem_description,
            context_chunks=context_chunks,
            force_regenerate=True
        )

        print(f"\n✅ Prompt regenerated successfully for ticket {ticket.ticket_number}")
        print(f"{'='*80}\n")

        return Response({
            "status": "success",
            "ticket_number": ticket.ticket_number,
            "camera_type": camera_type,
            "context_chunks_found": len(context_chunks),
            "message": "Ticket description updated successfully",
        })

    except Exception as e:
        import traceback
        print(f"\n❌ ERROR during prompt regeneration:")
        print(traceback.format_exc())
        return Response({
            "status": "partial_success",
            "message": f"Description updated, but prompt regeneration failed: {str(e)}"
        })



from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from chatbot.models import SupportTicket

# Your API key here or from environment/config
openai.api_key = "********************************************************************************************************************************************************************"

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from openai import OpenAIError  # Import specific OpenAI error

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from openai import OpenAIError
from collections import Counter

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_problem_description(request):
    ticket_number = request.data.get("ticket_number")
    raw_description = (request.data.get("problem_description") or "").strip()

    if not ticket_number or not raw_description:
        return Response({"error": "ticket_number and problem_description are required"}, status=400)

    # Fetch ticket that belongs to requesting user
    ticket = get_object_or_404(SupportTicket, ticket_number=ticket_number, user=request.user)

    # Check if ticket is in a valid state
    if ticket.status == "closed":
        return Response({"error": "Cannot add problem description to a closed ticket"}, status=400)

    # 1️⃣ Rewrite description
    try:
        rewrite_resp = track_openai_chat_completion(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Rewrite user complaints professionally."},
                {"role": "user", "content": raw_description},
            ],
            user=request.user,
            purpose="problem_description_rewrite"
        )
        cleaned_description = rewrite_resp.choices[0].message.content.strip()
    except OpenAIError as e:
        print(f"❌ OpenAI error during description rewrite: {e}")
        return Response({"error": f"Failed to rewrite description: {e}"}, status=500)
    except Exception as e:
        print(f"❌ Unexpected error during description rewrite: {e}")
        return Response({"error": f"Unexpected error: {e}"}, status=500)

    # Store refined problem text
    ticket.problem_description = cleaned_description
    ticket.save(update_fields=["problem_description"])

    # 2️⃣ Generate & save problem summary
    try:
        problem_summary = generate_gpt_summary(
            cleaned_description,
            "Summarize the following user problem in 2 lines:"
        ) or "No summary yet."
        ticket.problem_summary = problem_summary.strip()
        ticket.save(update_fields=["problem_summary"])
        print(f"✅ Problem summary saved for ticket {ticket.ticket_number}")
    except OpenAIError as e:
        print(f"❌ OpenAI error during problem summary generation: {e}")
    except Exception as e:
        print(f"❌ Unexpected error during problem summary generation: {e}")

    # 3️⃣ Build query with ticket metadata INCLUDING product_hierarchy_path
    db_ctx = {
        "productType": ticket.product_type,
        "purchasedFrom": ticket.purchased_from,
        "yearOfPurchase": ticket.year_of_purchase,
        "brand": ticket.brand,
        "modelNumber": ticket.model_number,
        "serialNumber": ticket.serial_number,
        "operatingSystem": ticket.operating_system_detailed,
        # ✅ Add camera-specific fields for Linea camera support
        "familyName": ticket.family_name,
        "interfaceType": ticket.interface_type,
        "colorType": ticket.color_type,
        "framegrabber": ticket.framegrabber,
        "sensorType": ticket.sensor_type,
        # ✅ CRITICAL: Include product_hierarchy_path for camera type inference
        "product_hierarchy_path": ticket.product_hierarchy_path if hasattr(ticket, 'product_hierarchy_path') else None,
    }
    
    full_query = build_full_query(
        user_query=cleaned_description,
        product_ctx=db_ctx,
        problem_description=cleaned_description,
        solution_summary=None
    )

    # ✅ Extract camera type BEFORE calling retrieve_and_generate_answer
    print(f"\n🎯 DEBUG: ===== CAMERA TYPE EXTRACTION START =====")
    print(f"🎯 DEBUG: db_ctx keys: {list(db_ctx.keys())}")
    print(f"🎯 DEBUG: product_hierarchy_path value: {db_ctx.get('product_hierarchy_path')}")
    print(f"🎯 DEBUG: familyName: {db_ctx.get('familyName')}")
    print(f"🎯 DEBUG: interfaceType: {db_ctx.get('interfaceType')}")
    print(f"🎯 DEBUG: colorType: {db_ctx.get('colorType')}")
    
    camera_type = extract_camera_type_from_context(db_ctx)
    
    print(f"🎯 DEBUG: Camera type extracted in add_problem_description: {camera_type}")
    
    if camera_type:
        weaviate_class = get_weaviate_class_for_camera_type(camera_type)
        print(f"✅ DEBUG: Using camera_type '{camera_type}' mapped to Weaviate class '{weaviate_class}'")
    else:
        print(f"⚠️ DEBUG: No camera_type detected - will search all classes")
    
    print(f"🎯 DEBUG: ===== CAMERA TYPE EXTRACTION END =====\n")

    # Run initial query with high certainty
    result = retrieve_and_generate_answer(
        full_query, 
        user_id=request.user.id, 
        top_k=10, 
        camera_type=camera_type,
        certainty_threshold=0.7
    )

    if not result or not result.get("answer") or result.get("answer") == "No answer.":
        # Retry with lower certainty
        print("🔄 No strong result. Retrying with lower certainty...")
        result = retrieve_and_generate_answer(
            full_query, 
            user_id=request.user.id, 
            top_k=10, 
            camera_type=camera_type, 
            certainty_threshold=0.5
        )

    if not result or not result.get("answer") or result.get("answer") == "No answer.":
        # Final fallback message
        answer_text = "I couldn't find any relevant support documents for your query. Please try rephrasing your question or contact support directly."
        file_scores = {}
        primary_reference_file = None
    else:
        answer_text = result.get("answer", "No answer.")
        file_scores = result.get("file_scores", {})
        primary_reference_file = result.get("primary_reference_file")

    # 📎 Download Offer Logic: Create file objects based on primary reference file
    file_objs = []
    if primary_reference_file:
        if isinstance(primary_reference_file, list):
            # Multiple files with nearly equal scores
            for filename in primary_reference_file:
                file_objs.append({
                    "filename": filename,
                    "url": f"/api/files/{filename}",
                    "score": file_scores.get(filename, 0.0)
                })
        else:
            # Single primary reference file
            file_objs = [{
                "filename": primary_reference_file,
                "url": f"/api/files/{primary_reference_file}",
                "score": file_scores.get(primary_reference_file, 0.0)
            }]

    # 5️⃣ Generate & save a two-line summary of the answer
    try:
        summary = generate_gpt_summary(
            answer_text,
            "Summarize the following solution in 2 lines:"
        ) or "No solution yet."
        ticket.solution_summary = summary.strip()
        ticket.save(update_fields=["solution_summary"])
        print(f"✅ Solution summary saved for ticket {ticket.ticket_number}")
    except OpenAIError as e:
        print(f"❌ OpenAI error during solution summary generation: {e}")
    except Exception as e:
        print(f"❌ Unexpected error during solution summary generation: {e}")

    # 6️⃣ Return to front-end
    return Response({
        "status": "success",
        "answer": answer_text,
        "files": file_objs,
    })


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from .models import SupportTicket


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from chatbot.models import SupportTicket  # Adjust import path as needed

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_ticket_status(request):
    ticket_number = request.data.get("ticket_number")
    status = request.data.get("status")  # Expected: 'open' or 'closed'

    if status not in ["open", "closed"]:
        return Response({"error": "Invalid status."}, status=400)

    if not ticket_number:
        return Response({"error": "Ticket number is required."}, status=400)

    try:
        # Find ticket by ticket_number and user
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    ticket.status = status
    ticket.save()
    return Response({"success": True, "message": f"Ticket {ticket_number} marked as {status}."})

from chatbot.models import SupportTicket  # Adjust import path if different

def get_camera_class_from_db(ticket_number):
    """
    ✅ Fetch interface_type and color_type from SupportTicket (MySQL)
    ✅ Map them directly to the correct Weaviate class name.
    """

    print(f"🎯 DEBUG: Fetching camera mapping for Ticket Number: {ticket_number}")

    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number)
    except SupportTicket.DoesNotExist:
        print(f"❌ ERROR: SupportTicket {ticket_number} not found in database.")
        return None

    # 🔍 Extract fields
    interface_type = (ticket.interface_type or "").lower()
    color_type = (ticket.color_type or "").lower()

    print(f"🎯 DEBUG: interface_type = '{interface_type}', color_type = '{color_type}'")

    # ✅ Mapping logic
    camera_class = None

    if "gige" in interface_type:
        if "color" in color_type:
            camera_class = "LineaGiGEColor"
        elif "mono" in color_type:
            camera_class = "LineaGiGEMono"

    elif "camera link" in interface_type or "cameralink" in interface_type or "cl" in interface_type:
        if "color" in color_type:
            camera_class = "LineaCameraLinkColor"
        elif "mono" in color_type:
            camera_class = "LineaCameraLinkMono"

    print(f"✅ DEBUG: Mapped Weaviate class = {camera_class}")
    return camera_class

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def view_ticket_prompt(request, ticket_number):
    """
    View the generated prompt for a specific ticket (for debugging/admin purposes).
    """
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number, user=request.user)
    except SupportTicket.DoesNotExist:
        return Response({"error": "Ticket not found."}, status=404)

    if not ticket.generated_prompt:
        return Response({"error": "No prompt generated for this ticket yet."}, status=404)

    return Response({
        "ticket_number": ticket.ticket_number,
        "generated_prompt": ticket.generated_prompt,
        "prompt_file_path": ticket.prompt_file_path,
        "last_updated": ticket.last_activity
    })


# === Configuration ===
# New Linea Camera Class Names
LINEA_GIGE_COLOR_CLASS = "LineaGiGEColor"
LINEA_GIGE_MONO_CLASS = "LineaGiGEMono"
LINEA_CAMERA_LINK_COLOR_CLASS = "LineaCameraLinkColor"
LINEA_CAMERA_LINK_MONO_CLASS = "LineaCameraLinkMono"

# ✅ Weaviate Class Mapping Dictionary
WEAVIATE_CLASS_MAP = {
    ("linea", "gige", "monochrome"): LINEA_GIGE_MONO_CLASS,
    ("linea", "gige", "color"): LINEA_GIGE_COLOR_CLASS,
    ("linea", "cameralink", "monochrome"): LINEA_CAMERA_LINK_MONO_CLASS,
    ("linea", "cameralink", "color"): LINEA_CAMERA_LINK_COLOR_CLASS,
    # Support variations with spaces
    ("linea", "camera link", "monochrome"): LINEA_CAMERA_LINK_MONO_CLASS,
    ("linea", "camera link", "color"): LINEA_CAMERA_LINK_COLOR_CLASS,
}


def infer_camera_type_from_hierarchy(hierarchy_path):
    """
    Infers camera_type from product hierarchy strings.
    Example input: ['Camera', 'Line Scan', 'Linea', 'Camera Link', 'Color']
    Returns: one of ('linea_gige_color', 'linea_gige_mono', 
                     'linea_cameralink_color', 'linea_cameralink_mono')
    """

    if not hierarchy_path:
        return None

    # Normalize text
    path_str = " ".join(hierarchy_path).lower() if isinstance(hierarchy_path, list) else str(hierarchy_path).lower()

    # --- LINEA CAMERAS ---
    if "linea" in path_str:
        # GigE Series
        if "gige" in path_str:
            if "color" in path_str:
                return "linea_gige_color"
            elif "mono" in path_str or "monochrome" in path_str:
                return "linea_gige_mono"
            else:
                # Default bias → color
                return "linea_gige_color"

        # Camera Link Series
        elif "camera link" in path_str or "cameralink" in path_str:
            if "color" in path_str:
                return "linea_cameralink_color"
            elif "mono" in path_str or "monochrome" in path_str:
                return "linea_cameralink_mono"
            else:
                # Default bias → color
                return "linea_cameralink_color"

    return None


# EMBEDDING_MODEL and GPT_MODEL are defined below

WEAVIATE_URL = "http://localhost:8080"
CACHE_CLASS_NAME = "CachedQuestions"
# Use the same embedding model that was used to create the database (3072 dimensions)
EMBEDDING_MODEL = "text-embedding-3-large"
GPT_MODEL = "gpt-4o-mini"

client = weaviate.Client(WEAVIATE_URL)
#openai_client = OpenAI(api_key="********************************************************************************************************************************************************************")  # Replace with your actual key

# Removed duplicate get_embedding function - using the one with error handling below


# === New Weaviate Class Setup ===
def create_linea_gige_color_class():
    """Create the Linea GiGE Color class in Weaviate."""
    schema = {
        "class": LINEA_GIGE_COLOR_CLASS,
        "description": "Document chunks for Linea GiGE Color camera manuals",
        "properties": [
            {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
            {"name": "chunk_number", "dataType": ["int"], "description": "Chunk index"},
            {"name": "content", "dataType": ["text"], "description": "Text content of the chunk"},
            {"name": "camera_type", "dataType": ["string"], "description": "Camera type (linea_gige_color/mono/etc.)"},
            {"name": "model_name", "dataType": ["string"], "description": "Model name for filtering"}
        ],
        "vectorizer": "none"
    }
    try:
        client.schema.create_class(schema)
        print(f"✅ Created Weaviate class: {LINEA_GIGE_COLOR_CLASS}")
    except Exception as e:
        if "already exists" in str(e).lower():
            print(f"ℹ️ Class {LINEA_GIGE_COLOR_CLASS} already exists")
        else:
            print(f"❌ Error creating {LINEA_GIGE_COLOR_CLASS}: {e}")


def create_linea_gige_mono_class():
    """Create the Linea GiGE Mono class in Weaviate."""
    schema = {
        "class": LINEA_GIGE_MONO_CLASS,
        "description": "Document chunks for Linea GiGE Mono camera manuals",
        "properties": [
            {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
            {"name": "chunk_number", "dataType": ["int"], "description": "Chunk index"},
            {"name": "content", "dataType": ["text"], "description": "Text content of the chunk"},
            {"name": "camera_type", "dataType": ["string"], "description": "Camera type (linea_gige_color/mono/etc.)"},
            {"name": "model_name", "dataType": ["string"], "description": "Model name for filtering"}
        ],
        "vectorizer": "none"
    }
    try:
        client.schema.create_class(schema)
        print(f"✅ Created Weaviate class: {LINEA_GIGE_MONO_CLASS}")
    except Exception as e:
        if "already exists" in str(e).lower():
            print(f"ℹ️ Class {LINEA_GIGE_MONO_CLASS} already exists")
        else:
            print(f"❌ Error creating {LINEA_GIGE_MONO_CLASS}: {e}")


def create_linea_cameralink_color_class():
    """Create the Linea CameraLink Color class in Weaviate."""
    schema = {
        "class": LINEA_CAMERA_LINK_COLOR_CLASS,
        "description": "Document chunks for Linea CameraLink Color camera manuals",
        "properties": [
            {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
            {"name": "chunk_number", "dataType": ["int"], "description": "Chunk index"},
            {"name": "content", "dataType": ["text"], "description": "Text content of the chunk"},
            {"name": "camera_type", "dataType": ["string"], "description": "Camera type (linea_gige_color/mono/etc.)"},
            {"name": "model_name", "dataType": ["string"], "description": "Model name for filtering"}
        ],
        "vectorizer": "none"
    }
    try:
        client.schema.create_class(schema)
        print(f"✅ Created Weaviate class: {LINEA_CAMERA_LINK_COLOR_CLASS}")
    except Exception as e:
        if "already exists" in str(e).lower():
            print(f"ℹ️ Class {LINEA_CAMERA_LINK_COLOR_CLASS} already exists")
        else:
            print(f"❌ Error creating {LINEA_CAMERA_LINK_COLOR_CLASS}: {e}")


def create_linea_cameralink_mono_class():
    """Create the Linea CameraLink Mono class in Weaviate."""
    schema = {
        "class": LINEA_CAMERA_LINK_MONO_CLASS,
        "description": "Document chunks for Linea CameraLink Mono camera manuals",
        "properties": [
            {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
            {"name": "chunk_number", "dataType": ["int"], "description": "Chunk index"},
            {"name": "content", "dataType": ["text"], "description": "Text content of the chunk"},
            {"name": "camera_type", "dataType": ["string"], "description": "Camera type (linea_gige_color/mono/etc.)"},
            {"name": "model_name", "dataType": ["string"], "description": "Model name for filtering"}
        ],
        "vectorizer": "none"
    }
    try:
        client.schema.create_class(schema)
        print(f"✅ Created Weaviate class: {LINEA_CAMERA_LINK_MONO_CLASS}")
    except Exception as e:
        if "already exists" in str(e).lower():
            print(f"ℹ️ Class {LINEA_CAMERA_LINK_MONO_CLASS} already exists")
        else:
            print(f"❌ Error creating {LINEA_CAMERA_LINK_MONO_CLASS}: {e}")


def chunk_document(text, chunk_size=400):
    """
    Split document into smaller chunks of specified token size.

    Args:
        text (str): Input document text
        chunk_size (int): Maximum tokens per chunk (default: 400)

    Returns:
        list: List of text chunks
    """
    import re

    # Simple sentence splitting
    sentences = re.split(r'[.!?]+', text)
    chunks = []
    current_chunk = ""

    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue

        # Rough token estimation (1 token ≈ 4 characters)
        estimated_tokens = len(current_chunk + " " + sentence) // 4

        if estimated_tokens <= chunk_size:
            current_chunk += (" " + sentence) if current_chunk else sentence
        else:
            if current_chunk:
                chunks.append(current_chunk.strip())
            current_chunk = sentence

    if current_chunk:
        chunks.append(current_chunk.strip())

    return chunks


def store_document_chunks(document_text, source_filename, model_name="Genie nano 5g", camera_type=None):
    """
    Chunk document and store in appropriate Linea camera class with embeddings.

    Args:
        document_text (str): Full document text
        source_filename (str): Name of the source file
        model_name (str): Model name for filtering (default: "Genie nano 5g")
        camera_type (str): Camera type (required: linea_gige_color, linea_gige_mono, linea_cameralink_color, linea_cameralink_mono)

    Returns:
        int: Number of chunks stored
    """
    try:
        # Map camera_type to class and creation function
        if camera_type == "linea_gige_color":
            class_name = LINEA_GIGE_COLOR_CLASS
            create_linea_gige_color_class()
        elif camera_type == "linea_gige_mono":
            class_name = LINEA_GIGE_MONO_CLASS
            create_linea_gige_mono_class()
        elif camera_type == "linea_cameralink_color":
            class_name = LINEA_CAMERA_LINK_COLOR_CLASS
            create_linea_cameralink_color_class()
        elif camera_type == "linea_cameralink_mono":
            class_name = LINEA_CAMERA_LINK_MONO_CLASS
            create_linea_cameralink_mono_class()
        else:
            raise ValueError(f"❌ Invalid camera_type: {camera_type}. Must be one of: linea_gige_color, linea_gige_mono, linea_cameralink_color, linea_cameralink_mono")

        # Chunk the document
        chunks = chunk_document(document_text)
        print(f"📄 Split document into {len(chunks)} chunks")

        stored_count = 0

        for idx, chunk_text in enumerate(chunks):
            if not chunk_text.strip():
                continue

            try:
                # Generate embedding for the chunk
                embedding = get_embedding(chunk_text)

                # Store in Weaviate
                client.data_object.create(
                    data_object={
                        "source_file": source_filename,
                        "chunk_number": idx + 1,
                        "content": chunk_text,
                        "camera_type": camera_type,
                        "model_name": model_name
                    },
                    class_name=class_name,
                    vector=embedding
                )
                stored_count += 1

            except Exception as e:
                print(f"❌ Error storing chunk {idx + 1}: {e}")
                continue

        print(f"✅ Stored {stored_count} chunks for {source_filename}")
        return stored_count

    except Exception as e:
        print(f"❌ Error in store_document_chunks: {e}")
        return 0


def search_cache(query_vector, certainty_threshold=0.9):
    try:
        near_vector = {"vector": query_vector, "certainty": certainty_threshold}
        result = client.query.get(CACHE_CLASS_NAME, ["text", "answer", "source_files"]) \
                        .with_near_vector(near_vector) \
                        .with_limit(1) \
                        .do()

        matches = result.get("data", {}).get("Get", {}).get(CACHE_CLASS_NAME, [])
        if matches:
            match = matches[0]
            return {
                "query_text": match["text"],
                "answer": match["answer"],
                "files": match.get("source_files", []),
            }
        return None

    except Exception as e:
        print("❌ Weaviate cache search error:", e)
        return None


# ───────────────────────────────────────────────────────────────────
# OLD:
# def add_to_cache(query_text, answer_text, vector):

def add_to_cache(query_text, answer_text, vector, source_files):
    """
    Store a Q/A plus its vector & related source files list.
    """
    try:
        client.data_object.create(
            data_object={
                "text": query_text,
                "answer": answer_text,
                "source_files": source_files,  # matches schema property name
            },
            class_name=CACHE_CLASS_NAME,
            vector=vector
        )
    except Exception as e:
        print("❌ Weaviate cache insert error:", e)





#client_openai = OpenAI(api_key="********************************************************************************************************************************************************************")
chat_history = []
from django.http import HttpResponse, Http404
from .models import PdfFile  # Replace with your actual model name

from urllib.parse import unquote

@api_view(['GET'])
def serve_file_view(request, filename):
    decoded_filename = unquote(filename)  # Decode %20, %28, %29 etc
    try:
        file_entry = PdfFile.objects.get(file_name=decoded_filename)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No such file: {decoded_filename}")



# === In-memory per-user chat history (not persistent) ===
chat_history_per_user = {}

def get_user_history(user_id):
    return chat_history_per_user.get(user_id, [])

def add_to_user_history(user_id, query, answer):
    history = chat_history_per_user.get(user_id, [])
    history.append((query, answer))
    if len(history) > 20:
        history = history[-20:]
    chat_history_per_user[user_id] = history

# === Signup View ===

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import CustomUser, ChatHistory
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.models import User
import json

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.views import TokenObtainPairView
from .serializers import EmailTokenObtainPairSerializer
from rest_framework.permissions import IsAuthenticated
User = get_user_model()


@api_view(['POST'])
@permission_classes([AllowAny])
def signup_view(request):
    data = request.data

    # Explicit required fields validation
    required_fields = ['state', 'name', 'address', 'organization', 'official_email', 'phone', 'mobile', 'password', 'password2']

    for field in required_fields:
        if not data.get(field):
            return Response({"error": f"Missing required field: {field}"}, status=status.HTTP_400_BAD_REQUEST)

    # Password match check
    if data['password'] != data['password2']:
        return Response({"error": "Passwords do not match"}, status=status.HTTP_400_BAD_REQUEST)

    # Check if user exists
    if User.objects.filter(official_email=data['official_email']).exists():
        return Response({"error": "User with this email already exists"}, status=status.HTTP_400_BAD_REQUEST)

    # Create user with provided data
    user = User.objects.create_user(
        official_email=data['official_email'],
        password=data['password'],
        state=data['state'],
        name=data['name'],
        address=data['address'],
        organization=data['organization'],
        alt_email=data.get('alt_email', ''),  # optional field
        phone=data['phone'],
        mobile=data['mobile'],
    )

    return Response({"message": "User created successfully"}, status=status.HTTP_201_CREATED)

class EmailLoginView(TokenObtainPairView):
    serializer_class = EmailTokenObtainPairSerializer


from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from datetime import datetime
from .upload import upload_pdf_to_db  # Your upload logic

from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse

@csrf_exempt
def upload_pdf_view(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        files = request.FILES.getlist('pdf_file')
        camera_type = request.POST.get('camera_type')

        if not files:
            return JsonResponse({'error': 'No files received'}, status=400)

        if not camera_type:
            return JsonResponse({'error': 'Camera type is required'}, status=400)

        if camera_type not in [
            'linea_gige_color',
            'linea_gige_mono',
            'linea_cameralink_color',
            'linea_cameralink_mono'
        ]:
            return JsonResponse({'error': 'Invalid camera type. Must be one of: linea_gige_color, linea_gige_mono, linea_cameralink_color, linea_cameralink_mono'}, status=400)

        success_count = 0
        for file in files:
            binary_data = file.read()
            filename = file.name
            uploaded = upload_pdf_to_db(filename, binary_data, datetime.now(), camera_type)
            if uploaded:
                success_count += 1

        return JsonResponse({'message': f'{success_count} file(s) uploaded successfully to {camera_type} category'}, status=200)
    except Exception as e:
        return JsonResponse({'error': f'Exception: {str(e)}'}, status=500)

    
    

from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import get_authorization_header
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.http import HttpResponse, Http404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from urllib.parse import unquote

from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework import exceptions

@api_view(['POST'])
@permission_classes([AllowAny])
def upload_document_to_linea_cameras(request):
    """
    Upload and process document for Linea camera classes.

    Expected POST data:
    - document_text: Full document text
    - source_filename: Name of the source file
    - model_name: Model name (optional, defaults to "Genie nano 5g")
    - camera_type: Camera type (optional - will be inferred from product_hierarchy_path if not provided)
    - product_hierarchy_path: Product hierarchy path (optional - used to infer camera_type)
    """
    try:
        document_text = request.data.get('document_text', '').strip()
        source_filename = request.data.get('source_filename', '').strip()
        model_name = request.data.get('model_name', 'Genie nano 5g')

        # ✅ Infer camera_type from product hierarchy if not explicitly provided
        product_hierarchy_path = request.data.get('product_hierarchy_path', [])
        camera_type = request.data.get('camera_type') or infer_camera_type_from_hierarchy(product_hierarchy_path)

        if not document_text:
            return Response({"error": "document_text is required"}, status=400)

        if not source_filename:
            return Response({"error": "source_filename is required"}, status=400)

        if not camera_type:
            return Response({"error": "Unable to infer camera type from product hierarchy. Please provide camera_type explicitly."}, status=400)

        # ✅ Log the inferred camera type before Weaviate operations
        print(f"📘 Inferred camera_type: {camera_type} from hierarchy: {product_hierarchy_path}")

        valid_camera_types = ["linea_gige_color", "linea_gige_mono", "linea_cameralink_color", "linea_cameralink_mono"]
        if camera_type not in valid_camera_types:
            return Response({"error": f"Invalid camera_type. Must be one of: {', '.join(valid_camera_types)}"}, status=400)

        # Process and store the document
        chunks_stored = store_document_chunks(
            document_text=document_text,
            source_filename=source_filename,
            model_name=model_name,
            camera_type=camera_type
        )

        if chunks_stored > 0:
            return Response({
                "status": "success",
                "message": f"Document processed successfully",
                "chunks_stored": chunks_stored,
                "source_filename": source_filename,
                "model_name": model_name,
                "camera_type": camera_type
            })
        else:
            return Response({
                "status": "error",
                "message": "Failed to process document"
            }, status=500)

    except Exception as e:
        return Response({
            "status": "error",
            "message": f"Error processing document: {str(e)}"
        }, status=500)


@api_view(['POST'])
@permission_classes([AllowAny])
def test_linea_cameras_search(request):
    """
    Test endpoint for Linea cameras search functionality.

    Expected POST data:
    - query: Search query text
    - model_name: Optional model name filter
    - camera_type: Optional camera type filter (linea_gige_color, linea_gige_mono, linea_cameralink_color, linea_cameralink_mono)
    - limit: Optional result limit (default: 5)
    """
    try:
        query = request.data.get('query', '').strip()
        model_name = request.data.get('model_name', None)
        camera_type = request.data.get('camera_type', None)
        limit = int(request.data.get('limit', 5))

        if not query:
            return Response({"error": "query is required"}, status=400)

        # Auto-detect model name if not provided
        if not model_name:
            model_name = detect_model_name_from_query(query)

        # Search Linea cameras
        results = search_linea_cameras_with_filter(
            query=query,
            model_name=model_name,
            camera_type=camera_type,
            limit=limit,
            certainty_threshold=0.7
        )

        # Format results for response
        formatted_results = []
        for result in results:
            formatted_results.append({
                "source_file": result.get("source_file"),
                "chunk_number": result.get("chunk_number"),
                "content": result.get("content", "")[:200] + "..." if len(result.get("content", "")) > 200 else result.get("content", ""),
                "camera_type": result.get("camera_type"),
                "model_name": result.get("model_name"),
                "certainty": result.get("_additional", {}).get("certainty", 0.0)
            })

        return Response({
            "status": "success",
            "query": query,
            "detected_model_name": model_name,
            "results_count": len(formatted_results),
            "results": formatted_results
        })

    except Exception as e:
        return Response({
            "status": "error",
            "message": f"Error testing search: {str(e)}"
        }, status=500)


@api_view(['GET'])
@permission_classes([AllowAny])
def serve_file_view(request, filename):
    # Try to authenticate from ?token= query param
    token = request.query_params.get('token')
    if token:
        try:
            validated_token = JWTAuthentication().get_validated_token(token)
            user = JWTAuthentication().get_user(validated_token)
            request.user = user
        except exceptions.AuthenticationFailed:
            return Response({"detail": "Invalid token."}, status=401)
    else:
        # You can also check request.headers for 'Authorization' if you want
        # or reject unauthenticated requests
        return Response({"detail": "Authentication credentials were not provided."}, status=401)

    decoded_filename = unquote(filename)
    try:
        file_entry = PdfFile.objects.get(file_name=decoded_filename)
        response = HttpResponse(file_entry.file_data, content_type='application/pdf')
        response['Content-Disposition'] = f'inline; filename="{decoded_filename}"'
        return response
    except PdfFile.DoesNotExist:
        raise Http404(f"No such file: {decoded_filename}")




def get_prompt_from_json(prompt_type):
    PROMPTS_JSON_PATH = r"D:\AI-Agent-Chatbot-main\chatbot\prompt.json"
    try:
        with open(PROMPTS_JSON_PATH, "r") as f:
            prompts = json.load(f)
        prompt_data = prompts.get(prompt_type)
        if not prompt_data or not prompt_data.get("is_active"):
            raise ValueError(f"No active prompt found for type: {prompt_type}")
        return prompt_data["template"]
    except Exception as e:
        raise ValueError(f"Prompt loading error: {str(e)}")


# ═══════════════════════════════════════════════════════════════════════════════
# PROMPT GENERATION AND MANAGEMENT FUNCTIONS
# ═══════════════════════════════════════════════════════════════════════════════

def get_ticket_chat_history(ticket):
    """
    Retrieve chat history for a specific ticket from the database.
    Returns a list of tuples: [(user_message, bot_response), ...]
    """
    chat_messages = ChatHistory.objects.filter(ticket=ticket).order_by('created_at')
    return [(msg.user_message, msg.bot_response) for msg in chat_messages]


def save_chat_message(ticket, user_message, bot_response):
    """
    Save a chat exchange to the database for a specific ticket.
    """
    ChatHistory.objects.create(
        ticket=ticket,
        user_message=user_message,
        bot_response=bot_response
    )


def generate_final_prompt(ticket, user_description, context_chunks):
    """
    Generate the final prompt according to the specified format.

    Args:
        ticket: SupportTicket instance
        user_description: Current user's problem description
        context_chunks: List of relevant document context chunks

    Returns:
        str: The formatted prompt
    """
    # Get chat history for this ticket
    chat_history = get_ticket_chat_history(ticket)

    # Format product details with hierarchical information
    hierarchy_path = ' > '.join(ticket.product_hierarchy_path) if ticket.product_hierarchy_path else 'Not specified'
    product_details = f"""[PRODUCT DETAILS]
- Product Hierarchy: {hierarchy_path}
- Product Category: {ticket.product_category or 'Not specified'}
- Product Subcategory: {ticket.product_subcategory or 'Not specified'}
- Product Family: {ticket.product_family or 'Not specified'}
- Product Interface: {ticket.product_interface or 'Not specified'}
- Brand: {ticket.brand or 'Not specified'}
- Sensor Type: {ticket.sensor_type or 'Not specified'}
- Model: {ticket.model_number or 'Not specified'}
- Serial Number: {ticket.serial_number or 'Not specified'}
- SDK: {ticket.sdk_software_used or 'Not specified'} ({ticket.sdk_version or 'Not specified'})
- Programming Language: {ticket.programming_language or 'Not specified'}
- Configuration Tool: {ticket.camera_configuration_tool or 'Not specified'}
- Operating System: {ticket.operating_system_detailed or 'Not specified'}"""

    # Format issue categories
    categories = ', '.join(ticket.problem_categories) if ticket.problem_categories else 'Not specified'
    issue_category = f"""[ISSUE CATEGORY]
- Category: {categories}"""

    # Format user's description
    user_desc_section = f"""[USER'S DESCRIPTION]
"{user_description or ticket.problem_description or 'No description provided'}" """

    # Format chat history
    chat_history_section = "[CHAT HISTORY]\nFull previous conversation with the user:"
    if chat_history:
        for i, (user_msg, bot_reply) in enumerate(chat_history, 1):
            chat_history_section += f"\n{i}. User: \"{user_msg}\"\n   Bot: \"{bot_reply}\""
    else:
        chat_history_section += "\n(No previous conversation)"

    # Format document context
    document_context = "[DOCUMENT CONTEXT]\n(Extracted chunks from uploaded user document or vector DB):"
    if context_chunks:
        for i, chunk in enumerate(context_chunks, 1):
            document_context += f"\n{i}. \"{chunk}\""
    else:
        document_context += "\n(No relevant document context found)"

    # Combine all sections
    final_prompt = f"""{product_details}

{issue_category}

{user_desc_section}

{chat_history_section}

{document_context}"""

    return final_prompt


def save_prompt_to_files(ticket, prompt):
    """
    Save the generated prompt to both MySQL and external JSON file.

    Args:
        ticket: SupportTicket instance
        prompt: The generated prompt string
    """
    import os

    # Save to MySQL
    ticket.generated_prompt = prompt

    # Create support_prompts directory if it doesn't exist
    prompts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'support_prompts')
    os.makedirs(prompts_dir, exist_ok=True)

    # Save to JSON file
    json_filename = f"{ticket.ticket_number}.json"
    json_filepath = os.path.join(prompts_dir, json_filename)

    prompt_data = {
        "ticket_number": ticket.ticket_number,
        "prompt": prompt,
        "last_updated": timezone.now().isoformat()
    }

    try:
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(prompt_data, f, indent=2, ensure_ascii=False)

        # Update the file path in the ticket
        ticket.prompt_file_path = json_filepath
        ticket.save(update_fields=['generated_prompt', 'prompt_file_path'])

        print(f"✅ Prompt saved for ticket {ticket.ticket_number}")
        return True
    except Exception as e:
        print(f"❌ Error saving prompt to file: {e}")
        # Still save to MySQL even if file save fails
        ticket.save(update_fields=['generated_prompt'])
        return False


def get_or_generate_ticket_prompt(ticket, user_description=None, context_chunks=None, force_regenerate=False):
    """
    Get existing prompt from MySQL or generate a new one if needed.

    Args:
        ticket: SupportTicket instance
        user_description: Current user's problem description (for updates)
        context_chunks: List of relevant document context chunks
        force_regenerate: Force regeneration even if prompt exists

    Returns:
        str: The prompt to use for GPT
    """
    # Check if we need to regenerate
    should_regenerate = (
        force_regenerate or
        not ticket.generated_prompt or
        (user_description and user_description != ticket.problem_description)
    )

    if should_regenerate:
        print(f"🎯 DEBUG: 🔄 Generating NEW TICKET PROMPT for ticket {ticket.ticket_number}")

        # Update problem description if provided
        if user_description and user_description != ticket.problem_description:
            ticket.problem_description = user_description
            ticket.save(update_fields=['problem_description'])

        # Generate new prompt
        prompt = generate_final_prompt(ticket, user_description, context_chunks or [])

        # Save to both MySQL and JSON file
        save_prompt_to_files(ticket, prompt)

        print(f"🎯 DEBUG: ✅ NEW TICKET PROMPT generated and saved ({len(prompt)} chars)")
        return prompt
    else:
        print(f"🎯 DEBUG: 📋 Using EXISTING TICKET PROMPT for ticket {ticket.ticket_number}")
        print(f"🎯 DEBUG: ✅ EXISTING TICKET PROMPT retrieved ({len(ticket.generated_prompt)} chars)")
        return ticket.generated_prompt

# === OpenAI + Weaviate helper functions ===

def get_embedding(text):
    try:
        if not text or not text.strip():
            print("❌ Empty text provided for embedding")
            return None

        response = openai.Embedding.create(input=text, model=EMBEDDING_MODEL)

        if not response or not response.data or len(response.data) == 0:
            print("❌ Empty response from OpenAI embedding API")
            return None

        embedding = response.data[0].embedding

        if not embedding:
            print("❌ Empty embedding returned from OpenAI")
            return None

        return embedding

    except Exception as e:
        print(f"❌ Error generating embedding: {e}")
        return None

def generate_answer(query, context_chunks, history_tuples, ticket=None):
    """
    Generate answer using either ticket-specific prompt or general chat prompt.
    Enhanced with anti-hallucination measures.
    """
    print(f"🎯 DEBUG: generate_answer called with {len(context_chunks)} context chunks")

    # Check if we have any context chunks
    if not context_chunks or all(not chunk.strip() for chunk in context_chunks):
        print("🎯 DEBUG: No valid context chunks - returning fallback message")
        return "No relevant support documents found. Please try rephrasing your question or contact support directly."

    # Log context chunks for debugging
    print(f"🎯 DEBUG: Context chunks:")
    for i, chunk in enumerate(context_chunks):
        print(f"  {i+1}. '{chunk[:100]}...' (length: {len(chunk)})")

    if ticket:
        # Use ticket-specific prompt system
        print(f"🎯 DEBUG: Using TICKET PROMPT for ticket {ticket.ticket_number}")
        try:
            # Get or generate the ticket prompt
            filled_prompt = get_or_generate_ticket_prompt(
                ticket=ticket,
                user_description=query,
                context_chunks=context_chunks
            )

            # Add the current query and anti-hallucination instructions
            filled_prompt += f"\n\n[CURRENT QUERY]\n\"{query}\""
            filled_prompt += f"\n\n[INSTRUCTIONS]\nAnswer briefly and precisely.\n\nBefore answering, check if the user's query provides enough information to give a complete response.\n\nIf not sufficient, ask a clarifying question to gather the missing context (e.g., \"Could you specify which camera model or SDK you are referring to?\") and then continue the conversation naturally once the user responds.\n\nPrioritize the context for your answer. If you don't find exact details about the product or problem, inform the user and request clarification.\n\nIf the query is clearly related to Online Solutions (its products, services, teams, or operations), answer based on general company knowledge — even if the context is partial.\n\nIf the query is unrelated to Online Solutions (e.g., other technical topics, generic advice, or unrelated tools), respond strictly with:\n👉 \"Please ask query related to Online Solutions products and services.\"\n\nBe concise but informative.\n\nInclude specific technical details when available.\n\nMaintain conversation continuity — remember prior context when possible.\n\nIf the user requests CODE generation, use your knowledge to answer only if it's relevant to the technical stack used by Online Solutions.\n\nBefore answering, identify all hardware (e.g., cameras, framegrabbers) and software (e.g., SDKs, camera tools) involved, relate them properly, and frame your response accordingly.\n\nOnly use information from the provided document context above. Always include the source file name when referencing information."

            print(f"🎯 DEBUG: Ticket prompt length: {len(filled_prompt)} characters")

        except Exception as e:
            print(f"❌ Error with ticket prompt: {e}")
            return f"❌ Ticket Prompt Error: {str(e)}"
    else:
        # Use general chat prompt system with anti-hallucination measures
        print(f"🎯 DEBUG: Using GENERAL CHAT PROMPT (no ticket)")
        context_text = "\n".join(context_chunks) if context_chunks else "No relevant context found."
        history_text = "\n".join(f"Q: {q}\nA: {a}" for q, a in history_tuples[-5:]) if history_tuples else "No previous conversation."

        # Create anti-hallucination prompt
        filled_prompt = f"""Based on the following support document context, answer the user's question.

DOCUMENT CONTEXT:
{context_text}

CHAT HISTORY:
{history_text}

USER QUESTION: {query}

INSTRUCTIONS:
Answer briefly and precisely.

Before answering, check if the user's query provides enough information to give a complete response.

If not sufficient, ask a clarifying question to gather the missing context (e.g., "Could you specify which camera model or SDK you are referring to?") and then continue the conversation naturally once the user responds.

Prioritize the context for your answer. If you don't find exact details about the product or problem, inform the user and request clarification.

If the query is clearly related to Online Solutions (its products, services, teams, or operations), answer based on general company knowledge — even if the context is partial.

If the query is unrelated to Online Solutions (e.g., other technical topics, generic advice, or unrelated tools), respond strictly with:
👉 "Please ask query related to Online Solutions products and services."

Be concise but informative.

Include specific technical details when available.

Maintain conversation continuity — remember prior context when possible.

If the user requests CODE generation, use your knowledge to answer only if it's relevant to the technical stack used by Online Solutions.

Before answering, identify all hardware (e.g., cameras, framegrabbers) and software (e.g., SDKs, camera tools) involved, relate them properly, and frame your response accordingly.

Only use information from the provided document context above. Always include the source file name when referencing information."""

        print(f"🎯 DEBUG: General prompt length: {len(filled_prompt)} characters")

    # Print the actual prompt being used for debugging
    print(f"🎯 DEBUG: ===== ACTUAL PROMPT BEING SENT TO GPT =====")
    print(filled_prompt[:500] + "..." if len(filled_prompt) > 500 else filled_prompt)
    print(f"🎯 DEBUG: ===== END OF PROMPT (total length: {len(filled_prompt)}) =====")

    try:
        completion = track_openai_chat_completion(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": filled_prompt}],
            temperature=0.9,  # Lower temperature to reduce hallucination
            max_tokens=2000,
            purpose="chat_response"
        )
        answer = completion.choices[0].message.content.strip()

        print(f"🎯 DEBUG: GPT response length: {len(answer)} characters")
        print(f"🎯 DEBUG: GPT response preview: '{answer[:200]}...'")

        # Save chat history for ticket mode
        if ticket:
            save_chat_message(ticket, query, answer)

        return answer
    except Exception as e:
        print(f"❌ OpenAI error: {e}")
        return f"❌ OpenAI error: {e}"



def get_weaviate_class_for_camera_type(camera_type):
    """
    Get the appropriate Weaviate class name based on camera type.
    Now enhanced to use dynamic flows.

    Args:
        camera_type (str): Camera type identifier (e.g., 'linea_gige_color')

    Returns:
        str: Weaviate class name or None if not found
    """
    # First try dynamic flows
    dynamic_class = get_weaviate_class_from_dynamic_flows(camera_type=camera_type)
    if dynamic_class:
        return dynamic_class

    # Fallback to static mapping for backward compatibility
    mapping = {
        "linea_gige_color": LINEA_GIGE_COLOR_CLASS,
        "linea_gige_mono": LINEA_GIGE_MONO_CLASS,
        "linea_cameralink_color": LINEA_CAMERA_LINK_COLOR_CLASS,
        "linea_cameralink_mono": LINEA_CAMERA_LINK_MONO_CLASS,
    }
    class_name = mapping.get(camera_type)
    if class_name:
        print(f"✅ DEBUG: Mapped camera_type '{camera_type}' to Weaviate class '{class_name}' (static mapping)")
    else:
        print(f"⚠️ DEBUG: No Weaviate class mapping found for camera_type '{camera_type}'")
    return class_name


def get_weaviate_class_from_ticket(ticket):
    """
    Get the appropriate Weaviate class name directly from a ticket's camera configuration.

    Args:
        ticket: SupportTicket instance

    Returns:
        str: Weaviate class name or None if not a Linea camera
    """
    if not ticket:
        return None

    # Check if this is a Linea camera
    family_name = (ticket.family_name or "").lower()
    if "linea" not in family_name:
        return None

    # Normalize interface and color types
    interface_type = (ticket.interface_type or "").lower().replace(" ", "")
    color_type = (ticket.color_type or "").lower()

    # Build lookup key
    lookup_key = ("linea", interface_type, color_type)

    # Try to find in mapping
    weaviate_class = WEAVIATE_CLASS_MAP.get(lookup_key)

    if weaviate_class:
        print(f"✅ DEBUG: Ticket {ticket.ticket_number} mapped to Weaviate class '{weaviate_class}'")
        print(f"   Family: {ticket.family_name}, Interface: {ticket.interface_type}, Color: {ticket.color_type}")
    else:
        print(f"⚠️ DEBUG: Could not map ticket {ticket.ticket_number} to Weaviate class")
        print(f"   Lookup key: {lookup_key}")
        print(f"   Available keys: {list(WEAVIATE_CLASS_MAP.keys())}")

    return weaviate_class


def get_weaviate_class_from_dynamic_flows(ticket=None, camera_type=None, flow_path=None):
    """
    Get the appropriate Weaviate class name from dynamic flows.

    Args:
        ticket: SupportTicket instance (optional)
        camera_type: Camera type string (optional)
        flow_path: List representing the flow path (optional)

    Returns:
        str: Weaviate class name or None if not found
    """
    from .models import DynamicFlow

    try:
        # Method 1: Direct flow path matching
        if flow_path:
            flow = DynamicFlow.objects.filter(
                flow_path=flow_path,
                is_active=True,
                weaviate_class_created=True
            ).first()
            if flow:
                print(f"✅ DEBUG: Found dynamic flow for path {flow_path}: {flow.weaviate_class_name}")
                return flow.weaviate_class_name

        # Method 2: Ticket-based matching
        if ticket:
            # Build flow path from ticket hierarchy
            ticket_flow_path = []
            if ticket.product_hierarchy_path:
                ticket_flow_path = ticket.product_hierarchy_path
            else:
                # Build from individual fields
                if ticket.product_family:
                    ticket_flow_path.append(ticket.product_family)
                if ticket.interface_type:
                    ticket_flow_path.append(ticket.interface_type)
                if ticket.color_type:
                    ticket_flow_path.append(ticket.color_type)

            if ticket_flow_path:
                flow = DynamicFlow.objects.filter(
                    flow_path=ticket_flow_path,
                    is_active=True,
                    weaviate_class_created=True
                ).first()
                if flow:
                    print(f"✅ DEBUG: Found dynamic flow for ticket {ticket.ticket_number}: {flow.weaviate_class_name}")
                    return flow.weaviate_class_name

        # Method 3: Camera type string matching
        if camera_type:
            # Try to find flows that match the camera type pattern
            flows = DynamicFlow.objects.filter(
                is_active=True,
                weaviate_class_created=True
            )

            for flow in flows:
                # Check if camera_type matches the flow pattern
                flow_string = '_'.join([part.lower() for part in flow.flow_path])
                if camera_type.lower() in flow_string or flow_string in camera_type.lower():
                    print(f"✅ DEBUG: Found dynamic flow for camera_type '{camera_type}': {flow.weaviate_class_name}")
                    return flow.weaviate_class_name

        print(f"⚠️ DEBUG: No dynamic flow found for ticket={ticket}, camera_type={camera_type}, flow_path={flow_path}")
        return None

    except Exception as e:
        print(f"❌ ERROR: Failed to get dynamic flow: {e}")
        return None


def get_all_available_weaviate_classes():
    """
    Get all available Weaviate classes (both static and dynamic).

    Returns:
        list: List of available Weaviate class names
    """
    from .models import DynamicFlow

    classes = []

    # Add static classes
    static_classes = [
        LINEA_GIGE_COLOR_CLASS,
        LINEA_GIGE_MONO_CLASS,
        LINEA_CAMERA_LINK_COLOR_CLASS,
        LINEA_CAMERA_LINK_MONO_CLASS,
        "CameraManualV4",  # Default fallback
        "SoftwareChunkV1"  # Software documentation
    ]
    classes.extend(static_classes)

    # Add dynamic classes
    try:
        dynamic_flows = DynamicFlow.objects.filter(
            is_active=True,
            weaviate_class_created=True
        )
        for flow in dynamic_flows:
            if flow.weaviate_class_name not in classes:
                classes.append(flow.weaviate_class_name)
    except Exception as e:
        print(f"⚠️ Warning: Could not fetch dynamic flows: {e}")

    return classes


def detect_model_name_from_query(query):
    """
    Parse user query to detect model name.

    Args:
        query (str): User query text

    Returns:
        str or None: Detected model name or None if not found
    """
    import re

    # Common model patterns - more specific patterns first
    model_patterns = [
        (r'genie\s+nano\s+5g', "Genie nano 5g"),
        (r'genie\s+nano\s+\d+g', "Genie nano 5g"),  # Default to 5g for any genie nano
        (r'genie\s+nano', "Genie nano 5g"),  # Default to 5g
        # Only match "basler" if followed by a model name, not just "basler camera"
        (r'basler\s+[a-z]+(?:\s+[a-z0-9]+)*(?!\s+camera)', None),  # Don't match "basler camera"
        (r'flir\s+\w+', None),
        (r'allied\s+vision\s+\w+', None),
        # Add more patterns as needed
    ]

    query_lower = query.lower()

    for pattern, default_name in model_patterns:
        match = re.search(pattern, query_lower)
        if match:
            if default_name:
                return default_name
            else:
                # For other brands, return None unless we have specific model mapping
                return None

    return None


def search_linea_cameras_with_filter(query, model_name=None, camera_type=None, limit=5, certainty_threshold=0.7):
    """
    Search Linea camera classes with optional model_name and camera_type filtering.

    Args:
        query (str): Search query
        model_name (str, optional): Model name to filter by
        camera_type (str, optional): Camera type to search in specific class
        limit (int): Maximum results to return
        certainty_threshold (float): Minimum certainty threshold

    Returns:
        list: Search results with metadata
    """
    try:
        print(f"🔍 Searching Linea cameras with query: '{query[:100]}...'")
        if model_name:
            print(f"🔍 Filtering by model_name: '{model_name}'")
        if camera_type:
            print(f"🔍 Filtering by camera_type: '{camera_type}'")

        # Generate query embedding
        query_embedding = get_embedding(query)
        if not query_embedding:
            print("❌ Failed to generate embedding")
            return []

        # Determine which classes to search
        if camera_type:
            class_name = get_weaviate_class_for_camera_type(camera_type)
            if not class_name:
                print(f"❌ Invalid camera_type: {camera_type}")
                return []
            classes_to_search = [class_name]
        else:
            # Search all Linea camera classes
            classes_to_search = [
                LINEA_GIGE_COLOR_CLASS,
                LINEA_GIGE_MONO_CLASS,
                LINEA_CAMERA_LINK_COLOR_CLASS,
                LINEA_CAMERA_LINK_MONO_CLASS
            ]

        all_results = []

        for class_name in classes_to_search:
            # Build the query
            query_builder = (
                client.query
                .get(class_name, ["source_file", "chunk_number", "content", "camera_type", "model_name"])
                .with_near_vector({"vector": query_embedding, "certainty": certainty_threshold})
                .with_additional(["certainty", "vector"])
                .with_limit(limit)
            )

            # Add model_name filter if specified
            if model_name:
                query_builder = query_builder.with_where({
                    "path": ["model_name"],
                    "operator": "Equal",
                    "valueText": model_name
                })

            try:
                response = query_builder.do()

                # Debug: Print the full response structure
                print(f"🔍 DEBUG: Response keys for {class_name}: {list(response.keys()) if response else 'None'}")

                # Add null checks for response structure
                if not response:
                    print(f"❌ Empty response from Weaviate for {class_name}")
                    continue

                data = response.get("data")
                if not data:
                    print(f"❌ No data in response for {class_name}")
                    continue

                get_data = data.get("Get")
                if not get_data:
                    print(f"❌ No Get data in response for {class_name}")
                    continue

                results = get_data.get(class_name)
                print(f"🔍 DEBUG: Raw results for '{class_name}': {results}")

                if results is None or (isinstance(results, list) and len(results) == 0):
                    print(f"❌ No results for class {class_name} in response")
                    # Try with lower certainty threshold
                    if certainty_threshold > 0.5:
                        print(f"🔄 Retrying {class_name} with lower certainty threshold (0.5)")
                        query_builder_retry = (
                            client.query
                            .get(class_name, ["source_file", "chunk_number", "content", "camera_type", "model_name"])
                            .with_near_vector({"vector": query_embedding, "certainty": 0.5})
                            .with_additional(["certainty", "vector"])
                            .with_limit(limit)
                        )

                        if model_name:
                            query_builder_retry = query_builder_retry.with_where({
                                "path": ["model_name"],
                                "operator": "Equal",
                                "valueText": model_name
                            })

                        response_retry = query_builder_retry.do()
                        if response_retry and response_retry.get("data", {}).get("Get", {}).get(class_name):
                            results = response_retry["data"]["Get"][class_name]
                            print(f"✅ Found {len(results)} results with lower certainty for {class_name}")
                        else:
                            continue
                    else:
                        continue

                # Ensure results is a list
                if not isinstance(results, list):
                    print(f"❌ Results is not a list for {class_name}: {type(results)}")
                    continue

                print(f"🔍 Found {len(results)} results in {class_name}")
                all_results.extend(results)

            except Exception as e:
                print(f"❌ Error searching {class_name}: {e}")
                continue

        # Sort all results by certainty (highest first) and limit
        if all_results:
            all_results.sort(key=lambda x: x.get("_additional", {}).get("certainty", 0.0), reverse=True)
            final_results = all_results[:limit]
            print(f"🔍 Total results found: {len(all_results)}, returning top {len(final_results)}")
            return final_results

        return []

    except Exception as e:
        print(f"❌ Error searching Linea cameras: {e}")
        return []

def extract_camera_type_from_context(context: dict) -> str:
    """
    Derives the correct camera_type identifier based on hierarchy, interface, color,
    and model number. Fixes cases where camera_type=None.

    Returns one of:
        'linea_cameralink_color', 'linea_cameralink_mono',
        'linea_gige_color', 'linea_gige_mono'

    Or None if camera type cannot be determined.
    """

    print(f"\n🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT START =====")
    print(f"🎯 DEBUG: Input context keys: {list(context.keys())}")

    # ✅ Handle product_hierarchy_path (can be list or string)
    hierarchy_raw = context.get("product_hierarchy_path")
    if isinstance(hierarchy_raw, list):
        hierarchy = " ".join(hierarchy_raw).lower()
        print(f"🎯 DEBUG: product_hierarchy_path (list): {hierarchy_raw}")
    elif isinstance(hierarchy_raw, str):
        hierarchy = hierarchy_raw.lower()
        print(f"🎯 DEBUG: product_hierarchy_path (string): {hierarchy_raw}")
    else:
        hierarchy = ""
        print(f"🎯 DEBUG: product_hierarchy_path is None or invalid type")

    interface = (context.get("interfaceType") or "").lower()
    color = (context.get("colorType") or "").lower()
    model = (context.get("modelNumber") or "").lower()
    family = (context.get("familyName") or "").lower()

    print(f"🎯 DEBUG: Extracted values:")
    print(f"  - hierarchy: '{hierarchy}'")
    print(f"  - interface: '{interface}'")
    print(f"  - color: '{color}'")
    print(f"  - model: '{model}'")
    print(f"  - family: '{family}'")

    # 1️⃣ First try to use infer_camera_type_from_hierarchy if we have hierarchy path
    if hierarchy_raw:
        inferred = infer_camera_type_from_hierarchy(hierarchy_raw)
        if inferred:
            print(f"✅ DEBUG: Inferred from hierarchy: {inferred}")
            print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
            return inferred

    # 2️⃣ Try based on explicit hierarchy text (if infer_camera_type_from_hierarchy failed)
    if "linea" in hierarchy or "linea" in family:
        if "camera link" in hierarchy or "cameralink" in hierarchy or "camera link" in interface or "cameralink" in interface:
            if "color" in hierarchy or "colour" in hierarchy or "color" in color or "colour" in color:
                print(f"✅ DEBUG: Matched Linea Camera Link Color")
                print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
                return "linea_cameralink_color"
            elif "mono" in hierarchy or "monochrome" in hierarchy or "mono" in color or "monochrome" in color:
                print(f"✅ DEBUG: Matched Linea Camera Link Mono")
                print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
                return "linea_cameralink_mono"
            else:
                # Default to color if color type is unclear
                print(f"⚠️ DEBUG: Defaulting to Linea Camera Link Color (color type unclear)")
                print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
                return "linea_cameralink_color"

        if "gige" in hierarchy or "gig e" in hierarchy or "gige" in interface:
            if "color" in hierarchy or "colour" in hierarchy or "color" in color or "colour" in color:
                print(f"✅ DEBUG: Matched Linea GigE Color")
                print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
                return "linea_gige_color"
            elif "mono" in hierarchy or "monochrome" in hierarchy or "mono" in color or "monochrome" in color:
                print(f"✅ DEBUG: Matched Linea GigE Mono")
                print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
                return "linea_gige_mono"
            else:
                # Default to color if color type is unclear
                print(f"⚠️ DEBUG: Defaulting to Linea GigE Color (color type unclear)")
                print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
                return "linea_gige_color"

    # 3️⃣ Try based on model patterns (common DALSA Linea prefixes)
    if model:
        if model.startswith("la-cc"):
            print(f"✅ DEBUG: Matched model prefix la-cc → Linea Camera Link Color")
            print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
            return "linea_cameralink_color"
        if model.startswith("la-cl"):
            print(f"✅ DEBUG: Matched model prefix la-cl → Linea Camera Link Mono")
            print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
            return "linea_cameralink_mono"
        if model.startswith("la-gc"):
            print(f"✅ DEBUG: Matched model prefix la-gc → Linea GigE Color")
            print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
            return "linea_gige_color"
        if model.startswith("la-gm"):
            print(f"✅ DEBUG: Matched model prefix la-gm → Linea GigE Mono")
            print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
            return "linea_gige_mono"

    # 4️⃣ Last resort
    print("❌ ERROR: extract_camera_type_from_context: Could not determine camera type from any available data.")
    print(f"🎯 DEBUG: ===== EXTRACT_CAMERA_TYPE_FROM_CONTEXT END =====\n")
    return None


def search_similar_chunks_weaviate(query, limit=5, camera_type=None, certainty_threshold=0.7):
    """
    Search semantically similar document chunks from Weaviate based on the query.

    ✅ Enhancements:
    - Enforces correct Linea class routing using camera_type
    - Prevents fallback to wrong classes (e.g., Mono instead of Color)
    - Adds detailed debug logging for each decision branch
    - Provides graceful fallback handling if Weaviate class is missing
    - ✅ CRITICAL: Returns empty list if camera_type is None to prevent cross-contamination
    """
    try:
        print(f"\n🔍 DEBUG: ===== WEAVIATE SEARCH START =====")
        print(f"🔍 Query: {query[:120]}...")
        print(f"🔍 Certainty threshold: {certainty_threshold}, Limit: {limit}")
        print(f"🔍 Input camera_type: {camera_type}")

        # ✅ CRITICAL FIX: Abort search if camera_type is None
        if camera_type is None:
            print(f"❌ ERROR: camera_type is None!")
            print(f"❌ Cannot perform Weaviate search without a valid camera_type.")
            print(f"❌ Aborting search to prevent searching all Linea classes.")
            print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END (ABORTED) =====\n")
            return []
        else:
            print(f"✅ camera_type provided: {camera_type}")

        # Step 1️⃣ Detect model name from the query (optional precision boost)
        detected_model = detect_model_name_from_query(query)
        print(f"🔍 DEBUG: Detected model name: {detected_model}")

        # Step 2️⃣ If model detected, use it to refine Linea search
        if detected_model:
            print(f"🔍 DEBUG: Searching Linea cameras with model filter: {detected_model}")
            linea_results = search_linea_cameras_with_filter(
                query=query,
                model_name=detected_model,
                camera_type=camera_type,
                limit=limit,
                certainty_threshold=certainty_threshold
            )
            if linea_results:
                print(f"✅ Found {len(linea_results)} results (with model filter)")
                print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
                return linea_results

        # Step 3️⃣ Fallback: try again without model filter
        print("🔍 DEBUG: Searching Linea cameras without model filter")
        linea_results = search_linea_cameras_with_filter(
            query=query,
            model_name=None,
            camera_type=camera_type,
            limit=limit,
            certainty_threshold=certainty_threshold
        )
        if linea_results:
            print(f"✅ Found {len(linea_results)} results (without model filter)")
            print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
            return linea_results

        # Step 4️⃣ Generate embedding for direct vector search
        print("🔍 DEBUG: No prefiltered Linea results, generating embedding...")
        query_embedding = get_embedding(query)
        
        if not query_embedding:
            print("❌ Failed to generate embedding")
            print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
            return []
        
        print(f"✅ DEBUG: Generated embedding with {len(query_embedding)} dimensions")

        client = weaviate.Client(url="http://localhost:8080")

        # Step 5️⃣ Determine which class to search (camera_type is guaranteed to be non-None at this point)
        class_name = get_weaviate_class_for_camera_type(camera_type)
        if not class_name:
            print(f"❌ ERROR: Invalid camera_type '{camera_type}' - no Weaviate class mapping found")
            print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
            return []

        classes_to_search = [class_name]
        print(f"🎯 DEBUG: Final camera_type = {camera_type}")
        print(f"🔍 DEBUG: Restricting search to class '{class_name}'")
        print(f"✅ DEBUG: ✅ SINGLE CLASS SEARCH CONFIRMED")

        # Step 6️⃣ Verify available classes in schema
        schema = client.schema.get()
        available_classes = [cls["class"] for cls in schema.get("classes", [])]
        print(f"🔍 DEBUG: Available Weaviate classes: {available_classes}")

        all_results = []

        # Step 7️⃣ Search through target class(es)
        for class_name in classes_to_search:
            if class_name not in available_classes:
                print(f"⚠️ Class '{class_name}' not found in Weaviate schema, skipping.")
                continue

            print(f"🔍 DEBUG: Searching in class '{class_name}'...")
            near_vector = {"vector": query_embedding, "certainty": certainty_threshold}

            try:
                response = (
                    client.query
                    .get(class_name, ["source_file", "content", "chunk_number", "camera_type"])
                    .with_additional(["certainty"])
                    .with_near_vector(near_vector)
                    .with_limit(limit)
                    .do()
                )

                results = response.get("data", {}).get("Get", {}).get(class_name, [])
                print(f"🔍 DEBUG: Retrieved {len(results)} results from '{class_name}'")

                if results:
                    for i, r in enumerate(results):
                        certainty = r.get("_additional", {}).get("certainty", 0.0)
                        src = r.get("source_file", "Unknown")
                        preview = r.get("content", "")[:80].replace("\n", " ")
                        print(f"  {i+1}. {src} (certainty={certainty:.3f}) → {preview}...")
                    all_results.extend(results)

            except Exception as e:
                print(f"❌ Weaviate query error for class '{class_name}': {e}")
                import traceback
                traceback.print_exc()
                continue

        # Step 8️⃣ Sort and limit results
        if not all_results:
            print("⚠️ No results found in any searched class.")
            print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
            return []

        sorted_results = sorted(
            all_results,
            key=lambda r: r.get("_additional", {}).get("certainty", 0.0),
            reverse=True,
        )

        print(f"✅ DEBUG: Total results retrieved: {len(sorted_results)}, returning top {limit}")
        print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
        
        return sorted_results[:limit]

    except Exception as e:
        print(f"❌ Fatal error in search_similar_chunks_weaviate: {e}")
        import traceback
        traceback.print_exc()
        print(f"🔍 DEBUG: ===== WEAVIATE SEARCH END =====\n")
        return []



from collections import defaultdict

from collections import defaultdict

LOG_FILE_PATH = "debug_log.txt"  # Change YourUserName accordingly

def log_debug(message: str):
    with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
        f.write(message + "\n")

def verify_weaviate_embeddings():
    """
    Verify that Linea camera collections have non-empty vector embeddings and metadata.
    """
    try:
        client = weaviate.Client(url="http://localhost:8080")

        print("🔍 DEBUG: Verifying Weaviate embeddings for Linea cameras...")

        # Check all Linea camera classes
        linea_classes = [
            LINEA_GIGE_COLOR_CLASS,
            LINEA_GIGE_MONO_CLASS,
            LINEA_CAMERA_LINK_COLOR_CLASS,
            LINEA_CAMERA_LINK_MONO_CLASS
        ]

        schema = client.schema.get()
        available_classes = [cls["class"] for cls in schema.get("classes", [])]

        verified_count = 0

        for class_name in linea_classes:
            print(f"\n🔍 Checking class: {class_name}")

            if class_name not in available_classes:
                print(f"⚠️ Collection '{class_name}' not found in Weaviate (may not be created yet)")
                continue

            # Get count of objects
            count_response = client.query.aggregate(class_name).with_meta_count().do()
            count = count_response.get("data", {}).get("Aggregate", {}).get(class_name, [{}])[0].get("meta", {}).get("count", 0)
            print(f"🔍 DEBUG: Collection '{class_name}' contains {count} objects")

            if count == 0:
                print(f"⚠️ Collection '{class_name}' is empty")
                continue

            # Sample a few objects to check embeddings and metadata
            sample_response = (
                client.query
                .get(class_name, ["source_file", "content", "chunk_number", "camera_type", "model_name"])
                .with_additional(["vector"])
                .with_limit(3)
                .do()
            )

            samples = sample_response.get("data", {}).get("Get", {}).get(class_name, [])

            for i, sample in enumerate(samples):
                source_file = sample.get("source_file", "Missing")
                content = sample.get("content", "")
                vector = sample.get("_additional", {}).get("vector", [])

                print(f"🔍 DEBUG: Sample {i+1}:")
                print(f"  - source_file: {source_file}")
                print(f"  - content length: {len(content)} chars")
                print(f"  - vector dimensions: {len(vector)}")
                print(f"  - content preview: '{content[:100]}...'")

                if not source_file or source_file == "Missing":
                    print(f"❌ Sample {i+1} missing source_file metadata")

                if not content:
                    print(f"❌ Sample {i+1} missing content")

                if not vector or len(vector) == 0:
                    print(f"❌ Sample {i+1} missing vector embedding")
                    return False

            verified_count += 1

        if verified_count > 0:
            print(f"\n✅ Weaviate embeddings verification passed for {verified_count} class(es)")
            return True
        else:
            print("\n⚠️ No Linea camera classes found with data")
            return False

    except Exception as e:
        print(f"❌ Error verifying Weaviate embeddings: {e}")
        return False

def retrieve_and_generate_answer(
    query,
    user_id=None,
    top_k=10,
    ticket=None,
    camera_type=None,
    certainty_threshold=0.7
):
    """
    ✅ FIXED: Retrieve relevant context from Weaviate (for Linea cameras) or MySQL PdfChunk,
    then generate GPT answer.
    
    This function:
    1. Determines camera class from ticket's interface_type and color_type
    2. Searches Weaviate for Linea camera documents (if applicable)
    3. Falls back to MySQL PdfChunk if Weaviate has no results
    4. Generates GPT answer using retrieved context
    
    Args:
        query (str): User's question
        user_id (int): User ID for history tracking
        top_k (int): Number of top results to retrieve (default: 10)
        ticket (SupportTicket): Ticket object with camera details
        camera_type (str): Camera type identifier (e.g., 'linea_gige_color')
        certainty_threshold (float): Minimum similarity score (default: 0.7)
    
    Returns:
        dict: {"answer": str, "file_scores": dict, "primary_reference_file": str or None}
    """
    import traceback
    import json
    import numpy as np
    from scipy.spatial.distance import cosine
    from openai import OpenAIError
    from chatbot.models import PdfChunk, SupportTicket

    print("\n" + "="*80)
    print("🔍 DEBUG: ===== RETRIEVE AND GENERATE ANSWER START =====")
    print("="*80)
    print(f"🎯 Query: {query[:100]}...")
    print(f"🎯 User ID: {user_id}")
    print(f"🎯 Ticket: {getattr(ticket, 'ticket_number', None)}")
    print(f"🎯 Camera Type (input): {camera_type}")
    print(f"🎯 Top K: {top_k}")
    print(f"🎯 Certainty Threshold: {certainty_threshold}")

    try:
        # ✅ STEP 1: Determine camera class from ticket or camera_type parameter
        camera_class = None
        weaviate_class = None

        if camera_type:
            # Use provided camera_type to get Weaviate class
            weaviate_class = get_weaviate_class_for_camera_type(camera_type)
            camera_class = camera_type.replace("_", "").title()
            print(f"✅ DEBUG: Using provided camera_type: {camera_type}")
            print(f"✅ DEBUG: Mapped to Weaviate class: {weaviate_class}")
            print(f"✅ DEBUG: Mapped to camera_class: {camera_class}")
        elif ticket:
            # Extract from ticket
            ticket_number = getattr(ticket, "ticket_number", None)
            print(f"🎯 DEBUG: Extracting camera info from Ticket: {ticket_number}")

            interface_type = (ticket.interface_type or "").lower().strip()
            color_type = (ticket.color_type or "").lower().strip()

            print(f"🎯 DEBUG: interface_type = '{interface_type}', color_type = '{color_type}'")

            # Map to Weaviate class names
            if "gige" in interface_type and "color" in color_type:
                weaviate_class = "LineaGiGEColor"
                camera_type = "linea_gige_color"
            elif "gige" in interface_type and "mono" in color_type:
                weaviate_class = "LineaGiGEMono"
                camera_type = "linea_gige_mono"
            elif "camera link" in interface_type and "color" in color_type:
                weaviate_class = "LineaCameraLinkColor"
                camera_type = "linea_cameralink_color"
            elif "camera link" in interface_type and "mono" in color_type:
                weaviate_class = "LineaCameraLinkMono"
                camera_type = "linea_cameralink_mono"

            camera_class = weaviate_class
            print(f"✅ DEBUG: Mapped to Weaviate class: {weaviate_class}")

        # ✅ STEP 2: Try Weaviate first (if camera_type is Linea)
        results = []
        source = "unknown"
        
        if weaviate_class:
            print(f"\n{'='*80}")
            print(f"🔍 STEP 2A: Searching Weaviate for class '{weaviate_class}'")
            print(f"{'='*80}")
            
            try:
                # Search Weaviate using existing function
                weaviate_matches = search_similar_chunks_weaviate(
                    query=query,
                    limit=top_k,
                    camera_type=camera_type
                )
                
                if weaviate_matches:
                    print(f"✅ Retrieved {len(weaviate_matches)} chunks from Weaviate")
                    for match in weaviate_matches:
                        results.append({
                            "source_file": match.get("source_file", "Unknown"),
                            "content": match.get("content", ""),
                            "similarity": match.get("certainty", 0.0),
                        })
                    source = "weaviate"
                else:
                    print(f"⚠️ No results from Weaviate for class '{weaviate_class}'")
            except Exception as e:
                print(f"⚠️ Weaviate search failed: {e}")
                print(traceback.format_exc())

        # ✅ STEP 2B: Fallback to MySQL PdfChunk if Weaviate returned nothing
        if not results:
            print(f"\n{'='*80}")
            print(f"🔍 STEP 2B: Falling back to MySQL PdfChunk")
            print(f"{'='*80}")
            
            # Fetch chunks from MySQL (filter by camera_type if available)
            if camera_type:
                chunks = PdfChunk.objects.filter(camera_type__icontains=camera_type)
                print(f"🎯 DEBUG: Filtering PdfChunk by camera_type containing '{camera_type}'")
            else:
                chunks = PdfChunk.objects.all()
                print(f"🎯 DEBUG: Searching all PdfChunk records (no camera_type filter)")

            print(f"✅ Retrieved {chunks.count()} chunks from MySQL")

            if not chunks.exists():
                print(f"⚠️ No chunks found in MySQL")
                return {
                    "answer": "The available documents do not contain sufficient information to answer your question.",
                    "file_scores": {},
                    "primary_reference_file": None
                }

            # Since PdfChunk doesn't have embeddings, return top chunks by creation date
            print(f"⚠️ WARNING: MySQL PdfChunk does not have embeddings.")
            print(f"⚠️ Returning top {top_k} chunks by creation date as fallback.")
            
            for chunk in chunks.order_by('-created_at')[:top_k]:
                results.append({
                    "source_file": chunk.source_file,
                    "content": chunk.content[:1000],  # Limit content length
                    "similarity": 0.5,  # Default similarity since we can't compute it
                })
            source = "mysql_fallback"

        print(f"\n{'='*80}")
        print(f"✅ STEP 3: Retrieved {len(results)} total results from {source}")
        print(f"{'='*80}")

        if not results:
            print("⚠️ No relevant documents found.")
            return {
                "answer": "The available documents do not contain sufficient information to answer your question.",
                "file_scores": {},
                "primary_reference_file": None
            }

        # ✅ STEP 4: Build GPT prompt with context
        context_text = "\n\n".join([
            f"[Source: {r['source_file']}]\n{r['content']}"
            for r in results
        ])
        
        file_scores = {r["source_file"]: r["similarity"] for r in results}
        primary_reference_file = results[0]["source_file"] if results else None

        prompt = f"""You are a professional technical assistant for Online Solutions.
Use ONLY the context provided below to answer the question.
If the context does not contain the answer, clearly state that you don't have enough information.

Context:
{context_text}

User Question:
{query}

Answer clearly and concisely in a technical and factual tone:"""

        print(f"\n{'='*80}")
        print(f"🤖 STEP 5: Generating GPT answer")
        print(f"{'='*80}")
        print(f"📝 Prompt length: {len(prompt)} characters")
        print(f"📝 Context chunks: {len(results)}")

        # ✅ STEP 5: Generate GPT answer
        response = track_openai_chat_completion(
            model=GPT_MODEL,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
            max_tokens=500,
            purpose="mysql_retrieval"
        )

        answer = response.choices[0].message.content.strip()
        print(f"✅ GPT answer generated: {len(answer)} characters")
        print(f"📄 Answer preview: {answer[:200]}...")

        # ✅ STEP 6: Cache and save to history
        try:
            query_embedding = get_embedding(query) if source != "weaviate" else None
            add_to_cache(query, answer, query_embedding, list(file_scores.keys()))
            add_to_user_history(user_id, query, answer)
            print(f"✅ Cached answer and saved to user history")
        except Exception as e:
            print(f"⚠️ Failed to cache/save history: {e}")

        print(f"\n{'='*80}")
        print(f"✅ DEBUG: ===== RETRIEVE AND GENERATE ANSWER END =====")
        print(f"{'='*80}\n")

        return {
            "answer": answer,
            "file_scores": file_scores,
            "primary_reference_file": primary_reference_file,
        }

    except OpenAIError as e:
        print(f"❌ OpenAI API Error: {e}")
        print(traceback.format_exc())
        return {
            "answer": "Error communicating with AI service. Please try again.",
            "file_scores": {},
            "primary_reference_file": None
        }
    except Exception as e:
        print(f"❌ ERROR in retrieve_and_generate_answer: {e}")
        print(traceback.format_exc())
        return {
            "answer": "An internal error occurred. Please try again or contact support.",
            "file_scores": {},
            "primary_reference_file": None
        }


def is_file_relevant_to_query(query_text, filename, matches, camera_type=None, min_certainty=0.6):
    """
    Check if a file is relevant to the user's query by analyzing:
    1. Camera type/brand matching
    2. Minimum certainty threshold for chunks from this file
    3. Content relevance analysis
    """
    if not query_text or not filename or not matches:
        return False

    query_lower = query_text.lower()
    filename_lower = filename.lower()

    # Extract camera brand/type from query
    camera_brands = ['dalsa', 'flir', 'basler', 'jai', 'allied', 'vision', 'teledyne']
    query_brands = [brand for brand in camera_brands if brand in query_lower]

    # Extract camera brand/type from filename
    file_brands = [brand for brand in camera_brands if brand in filename_lower]

    # Define brand-specific product mappings (including DALSA products that don't have "dalsa" in filename)
    brand_specific_terms = {
        'dalsa': ['genie', 'nano', 'linea'],  # Linea is a DALSA product
        'flir': ['spinnaker', 'blackfly', 'grasshopper'],
        'basler': ['ace', 'dart', 'pilot'],
        'teledyne': ['xtium', 'falcon']
    }

    # If query mentions specific camera brand, check for brand match
    if query_brands:
        # Check for specific product lines that might not match general brand queries
        specific_products = ['linea', 'genie', 'nano', 'spinnaker', 'blackfly', 'grasshopper']
        query_has_specific_product = any(product in query_lower for product in specific_products)
        file_has_specific_product = any(product in filename_lower for product in specific_products)

        # Check if file matches any of the query brands (either by brand name or brand-specific products)
        file_matches_brand = False

        if file_brands:
            # File has explicit brand name
            file_matches_brand = any(qb in file_brands for qb in query_brands)
        else:
            # Check if filename contains any brand-specific terms
            for brand in query_brands:
                if brand in brand_specific_terms:
                    if any(term in filename_lower for term in brand_specific_terms[brand]):
                        file_matches_brand = True
                        break

        if not file_matches_brand:
            print(f"🔍 DEBUG: File {filename} has no brand match for query brands: {query_brands}")
            return False

        # Additional check: If query is general (e.g., "DALSA camera") but file is very specific (e.g., "Linea"),
        # and user didn't mention that specific product, it might not be the best match
        if file_has_specific_product and not query_has_specific_product:
            # For now, allow it but could add more specific filtering here if needed
            # This allows DALSA queries to match Linea manuals, but could be refined
            pass

    # Check if any chunks from this file meet minimum certainty
    file_chunks = [m for m in matches if m.get("source_file") == filename]
    if not file_chunks:
        return False

    max_certainty = max(m.get("_additional", {}).get("certainty", 0.0) for m in file_chunks)
    if max_certainty < min_certainty:
        print(f"🔍 DEBUG: File {filename} max certainty {max_certainty:.3f} below threshold {min_certainty}")
        return False

    # Additional relevance checks for specific camera types
    if camera_type:
        camera_type_lower = camera_type.lower()
        if camera_type_lower not in filename_lower and camera_type_lower not in query_lower:
            # Check if file content mentions the camera type
            file_content = " ".join([m.get("content", "") for m in file_chunks]).lower()
            if camera_type_lower not in file_content:
                print(f"🔍 DEBUG: File {filename} doesn't match camera type {camera_type}")
                return False

    print(f"🔍 DEBUG: File {filename} passed relevance checks")
    return True

# === Chat Endpoint (protected) ===

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from chatbot.models import SupportTicket
from collections import Counter



# ── helper --------------------------------------------------------------

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from scipy.spatial.distance import cosine
from collections import Counter

def build_full_query(user_query, product_ctx, problem_description=None, solution_summary=None):
    """
    Build a rich query string by combining user query, product context, problem description, and solution summary.

    Args:
        user_query (str): The user's input query.
        product_ctx (dict): Dictionary containing product details (e.g., productType, model).
        problem_description (str, optional): The ticket's problem description.
        solution_summary (str, optional): The ticket's solution summary.

    Returns:
        str: A formatted query string with all relevant context.
    """
    ctx_lines = [
        f"Product Type: {product_ctx.get('productType', '')}",
        f"Purchased From: {product_ctx.get('purchasedFrom', '')}",
        f"Year of Purchase: {product_ctx.get('yearOfPurchase', '')}",
        f"Product Name: {product_ctx.get('productName', '')}",
        f"Model: {product_ctx.get('modelNumber', '') or product_ctx.get('model', '')}",
        f"Serial Number: {product_ctx.get('serialNumber', '') or product_ctx.get('serialNo', '')}",
        f"Operating System: {product_ctx.get('operatingSystem', '')}",
    ]

    # ✅ Add camera-specific fields for better context
    if product_ctx.get('familyName'):
        ctx_lines.append(f"Camera Family: {product_ctx.get('familyName', '')}")
    if product_ctx.get('interfaceType'):
        ctx_lines.append(f"Interface Type: {product_ctx.get('interfaceType', '')}")
    if product_ctx.get('colorType'):
        ctx_lines.append(f"Color Type: {product_ctx.get('colorType', '')}")
    if product_ctx.get('framegrabber'):
        ctx_lines.append(f"Frame Grabber: {product_ctx.get('framegrabber', '')}")

    if problem_description:
        ctx_lines.append(f"Problem Description: {problem_description}")
    if solution_summary:
        ctx_lines.append(f"Previous Solution: {solution_summary}")
    context = "\n".join([line for line in ctx_lines if line])
    return f"{context}\n\nUser Query: {user_query}"

from scipy.spatial.distance import cosine

def is_query_related(query_text, ticket, similarity_threshold=0.70):
    """
    Check if the query is related to the ticket's problem description, metadata, and solution summary.
    Returns True if related or if the query is a clarification request, False otherwise.
    """
    if not query_text or not ticket:
        return False

    # List of clarification keywords that are inherently related to the ticket
    clarification_keywords = [
        "elaborate", "more details", "explain", "clarify", "further", "more info",
        "expand", "detail", "describe", "tell me more"
    ]
    if any(keyword in query_text.lower() for keyword in clarification_keywords):
        print(f"DEBUG ▸ Query '{query_text}' identified as a clarification request")
        return True

    # Combine ticket metadata, problem description, and solution summary
    metadata = [
        ticket.product_type or "",
        ticket.purchased_from or "",
        ticket.year_of_purchase or "",
        ticket.brand or "",
        ticket.model_number or "",
        ticket.serial_number or "",
        ticket.operating_system_detailed or "",
        ticket.problem_description or "",
        ticket.solution_summary or "",
    ]
    combined_text = "\n".join([field for field in metadata if field])

    if not combined_text:
        return False

    try:
        query_embedding = get_embedding(query_text)
        ticket_embedding = get_embedding(combined_text)
        similarity = 1 - cosine(query_embedding, ticket_embedding)
        print(f"DEBUG ▸ Query similarity score: {similarity}")
        return similarity >= similarity_threshold
    except Exception as e:
        print(f"Error checking query relevance: {e}")
        return False
    
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from scipy.spatial.distance import cosine

@api_view(["POST"])
@permission_classes([IsAuthenticated])
def chat(request):
    """
    POST body expected:
    {
        "query"          : "user text",
        "ticket_id"      : "TCKT-123...",
        "ticket_mode"    : true | false,
        "stage"          : "await_close" | "unrelated_query" | "create_new_ticket" | "",
        "product_context": { ... }            # optional – keys see build_full_query()
    }
    """
    user = request.user
    query = (request.data.get("query") or "").strip()
    stage = request.data.get("stage", "")
    tmode = bool(request.data.get("ticket_mode", False))
    tid = request.data.get("ticket_id")
    pctx = request.data.get("product_context") or {}

    if not query:
        return Response({"error": "Query is required"}, status=400)

    print(f"\n=== DEBUG ▸ New chat call – user:{user.id} ticket_mode:{tmode} stage:{stage} query:{query}")

    # ── ticket fetch (if ticket_mode) ────────────────────────────────────
    ticket = None
    if tmode and tid:
        try:
            ticket = SupportTicket.objects.get(ticket_number=tid, user=user)
            print("DEBUG ▸ Ticket found:", ticket.ticket_number)

            # Check if this is the first message and ticket needs problem description
            if not ticket.problem_description:
                print(f"🎯 DEBUG ▸ FIRST MESSAGE: Collecting problem description for ticket {ticket.ticket_number}")
                # This is the problem description - save it and generate initial prompt
                ticket.problem_description = query.strip()

                # Generate problem summary
                try:
                    problem_summary = generate_gpt_summary(
                        ticket.problem_description,
                        "Summarize this problem description clearly and professionally:"
                    )
                    if problem_summary:
                        ticket.problem_summary = problem_summary
                except Exception as e:
                    print(f"❌ Error generating problem summary: {e}")

                ticket.save(update_fields=['problem_description', 'problem_summary'])
                print(f"🎯 DEBUG ▸ ✅ Problem description saved for ticket {ticket.ticket_number}")

                # Generate initial prompt with document context
                try:
                    # ✅ Build product context from ticket to infer camera type
                    product_context = {
                        "product_hierarchy_path": ticket.product_hierarchy_path if hasattr(ticket, 'product_hierarchy_path') else None,
                        "familyName": ticket.family_name if hasattr(ticket, 'family_name') else None,
                        "interfaceType": ticket.interface_type if hasattr(ticket, 'interface_type') else None,
                        "colorType": ticket.color_type if hasattr(ticket, 'color_type') else None,
                    }

                    # ✅ Infer camera type from product context
                    camera_type = extract_camera_type_from_context(product_context)

                    # ✅ Log the inferred camera type and corresponding Weaviate class
                    if camera_type:
                        class_name = get_weaviate_class_for_camera_type(camera_type)
                        print(f"🎯 DEBUG: Using class '{class_name}' for camera_type '{camera_type}'")
                        print(f"📘 DEBUG: Inferred from hierarchy: {product_context.get('product_hierarchy_path')}")
                    else:
                        print(f"⚠️ DEBUG: Could not infer camera_type from ticket {ticket.ticket_number}")

                    # ✅ Get relevant document context, constrained to the inferred class
                    matches = search_similar_chunks_weaviate(query, limit=5, camera_type=camera_type)
                    context_chunks = [m["content"] for m in matches] if matches else []

                    # Generate and save the initial prompt
                    get_or_generate_ticket_prompt(
                        ticket=ticket,
                        user_description=query,
                        context_chunks=context_chunks,
                        force_regenerate=True
                    )
                    print(f"🎯 DEBUG ▸ ✅ Initial prompt generated for ticket {ticket.ticket_number}")
                except Exception as e:
                    print(f"❌ Error generating initial prompt: {e}")
            else:
                print(f"🎯 DEBUG ▸ FOLLOW-UP MESSAGE: Using existing ticket prompt for {ticket.ticket_number}")

        except SupportTicket.DoesNotExist:
            print("DEBUG ▸ Ticket NOT found:", tid)
            return Response({"error": "Invalid ticket ID"}, status=400)

    # ── handle unrelated query response ──────────────────────────────────
    if stage == "unrelated_query" and ticket:
        low = query.lower()
        if low == "yes":
            # User wants to create a new ticket
            return Response({
                "answer": "Alright, let's create a new support ticket. Please provide the product type.",
                "ticket_status": ticket.status,
                "stage": "create_new_ticket",
                "files": [],
            })
        elif low == "no":
            # Ask if the user wants to close the current ticket
            return Response({
                "answer": "Okay, do you want to close the current ticket? (yes/no)",
                "ticket_status": ticket.status,
                "stage": "await_close",
                "files": [],
            })
        else:
            return Response({
                "answer": "Please answer 'yes' or 'no'. Do you want to create a new ticket?",
                "ticket_status": ticket.status,
                "stage": "unrelated_query",
                "files": [],
            })

    # ── close / escalate branch ──────────────────────────────────────────
    if stage == "await_close" and ticket:
        low = query.lower()
        if "escalate" in low:
            ticket.status = "escalated"
            ticket.save(update_fields=["status"])
            return Response({
                "answer": "Your ticket has been escalated. The technical support team will contact you ASAP.",
                "ticket_status": "escalated",
                "stage": "",
                "files": [],
            })
        if low in {"yes", "y", "close"}:
            ticket.status = "closed"
            ticket.save(update_fields=["status"])
            return Response({
                "answer": f"✅ Ticket {ticket.ticket_number} has been closed. Thank you!",
                "ticket_status": "closed",
                "stage": "",
                "files": [],
            })
        return Response({
            "answer": "Okay, ticket will remain open.",
            "ticket_status": "open",
            "stage": "",
            "files": [],
        })

    # ── build a single rich query string ─────────────────────────────────
    if ticket:
        db_ctx = {
            "productType":     ticket.product_type,
            "purchasedFrom":   ticket.purchased_from,
            "yearOfPurchase":  ticket.year_of_purchase,
            "brand":           ticket.brand,
            "modelNumber":     ticket.model_number,
            "serialNumber":    ticket.serial_number,
            "operatingSystem": ticket.operating_system_detailed,
            # ✅ Add camera-specific fields for Linea camera support
            "familyName":      ticket.family_name,
            "interfaceType":   ticket.interface_type,
            "colorType":       ticket.color_type,
            "framegrabber":    ticket.framegrabber,
            "sensorType":      ticket.sensor_type,
        }
        full_query = build_full_query(
            user_query=query,
            product_ctx=db_ctx,
            problem_description=ticket.problem_description,
            solution_summary=ticket.solution_summary or "",  # Include solution_summary
        )
    else:
        full_query = build_full_query(
            user_query=query,
            product_ctx=pctx,
            problem_description=pctx.get("problemDescription"),
        )

    # ── check if query is related to ticket (in ticket mode, after problem description) ─────────────
    if tmode and ticket and ticket.problem_description and query != "Please help me with this issue":
        print(f"DEBUG ▸ Checking if query '{query}' is related to ticket {ticket.ticket_number}")
        print(f"DEBUG ▸ Ticket problem description: {ticket.problem_description[:100]}...")
        if not is_query_related(query, ticket):
            print("DEBUG ▸ Query unrelated to ticket:", ticket.ticket_number)
            return Response({
                "answer": f"Your query seems unrelated to ticket {ticket.ticket_number}. Would you like to create a new ticket for this issue?",
                "ticket_status": ticket.status,
                "stage": "unrelated_query",
                "files": [],
            })
        else:
            print("DEBUG ▸ Query is related to ticket, proceeding with normal processing")

    # ── cache check ──────────────────────────────────────────────────────
    if not tmode:
        print("DEBUG ▸ Ticket mode is OFF - running cache check…")
        vec = get_embedding(full_query)
        cached = search_cache(vec, certainty_threshold=0.95)
        if cached:
            print("DEBUG ▸ Cache HIT - returning cached answer")
            return Response({
                "query": query,
                "answer": cached["answer"],
                "source": "cache",
                "cached_query": cached["query_text"],
                "matches": [],
                "files": cached["files"],
                "stage": "",
            })
        print("DEBUG ▸ Cache MISS - no cached answer found")
    else:
        print("DEBUG ▸ Ticket mode is ON - skipping cache check and querying GPT fresh")

    # ── retrieval + LLM answer ───────────────────────────────────────────
    print("DEBUG ▸ Querying Weaviate for retrieval chunks…")

    # Extract camera type from product context for targeted search
    camera_type = extract_camera_type_from_context(pctx)
    if camera_type:
        print(f"DEBUG ▸ Detected camera type: {camera_type}")
    else:
        print("DEBUG ▸ No specific camera type detected, searching all categories")

    # Use the updated retrieve_and_generate_answer function
    result = retrieve_and_generate_answer(full_query, user_id=user.id, top_k=10, ticket=ticket, camera_type=camera_type)
    answer = result.get("answer", "No answer available.")
    matches = result.get("matches", [])
    file_scores = result.get("file_scores", {})
    primary_reference_file = result.get("primary_reference_file")

    print(f"DEBUG ▸ {len(matches)} chunks retrieved")
    print(f"🎯 DEBUG ▸ GPT answer generated (length): {len(answer)} characters")

    # 📎 Download Offer Logic: Create file objects based on primary reference file
    file_objs = []
    if primary_reference_file:
        if isinstance(primary_reference_file, list):
            # Multiple files with nearly equal scores
            for filename in primary_reference_file:
                file_objs.append({
                    "filename": filename,
                    "url": f"/api/files/{filename}",
                    "score": file_scores.get(filename, 0.0)
                })
        else:
            # Single primary reference file
            file_objs = [{
                "filename": primary_reference_file,
                "url": f"/api/files/{primary_reference_file}",
                "score": file_scores.get(primary_reference_file, 0.0)
            }]

    # ── save two-line summary (ticket mode) ──────────────────────────────
    if ticket:
        try:
            summ_src = answer if answer and len(answer.strip()) >= 10 else f"Brief answer:\n{answer}"
            summ = generate_gpt_summary(summ_src, "Summarize in 2 lines:")
            ticket.solution_summary = (summ or "No solution yet.")
            ticket.save(update_fields=["solution_summary"])
            print(f"✅ Solution summary saved for ticket {ticket.ticket_number}")
        except Exception as e:
            print("DEBUG ▸ summary save error:", e)

    # ── Enhanced follow-up handling ───────────────────────────────────────────────
    next_stage = ""

    # ── final response ───────────────────────────────────────────────────
    return Response({
        "query": query,
        "answer": answer,
        "matches": matches,
        "files": file_objs,
        "source": "gpt",
        "stage": next_stage,
        "ticket_context": {
            "ticket_number": ticket.ticket_number if ticket else None,
            "product_info": f"{ticket.brand} {ticket.product_type} - {ticket.model_number}" if ticket and ticket.brand and ticket.model_number else f"{ticket.product_type} - {ticket.model_number}" if ticket and ticket.model_number else None,
        } if ticket else None,
    })




from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from .models import SupportTicket
from .serializers import (
    EscalatedTicketListSerializer,
    EscalatedTicketDetailSerializer,
)
@api_view(["GET"])
@permission_classes([IsAdminUser])
def escalated_tickets(request):
    qs = SupportTicket.objects.filter(status="escalated").order_by("-created_at")
    data = EscalatedTicketListSerializer(qs, many=True).data
    return Response(data)


@api_view(["GET"])
@permission_classes([IsAdminUser])
def escalated_ticket_detail(request, ticket_number):
    try:
        ticket = SupportTicket.objects.get(ticket_number=ticket_number)
    except SupportTicket.DoesNotExist:
        return Response({"detail": "Ticket not found."}, status=404)
    data = EscalatedTicketDetailSerializer(ticket).data
    return Response(data)



import os
import subprocess
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response

@api_view(["POST"])
@permission_classes([IsAdminUser])
def run_processing_pipeline(request):
    """
    Processing pipeline:
    1. Chunk PDFs (chunking.py)
    2. Push vectors to Weaviate (vector_embedding.py)
    All scripts must be located inside the 'chatbot' folder.
    """

    # Get absolute path to your Django project root
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    chatbot_dir = os.path.join(BASE_DIR, "chatbot")

    # Use current Python interpreter (works with any environment)
    import sys
    venv_python = sys.executable

    # Define scripts in execution order
    scripts = [
        # {"name": "img2.py", "args": []},
        {"name": "chunking.py", "args": []},
        {"name": "vector_embedding.py", "args": ["--weaviate"]},
    ]

    for script in scripts:
        script_name = script["name"]
        script_args = script["args"]
        script_path = os.path.join(chatbot_dir, script_name)

        if os.path.exists(script_path):
            try:
                command = [venv_python, script_path] + script_args
                result = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    check=True
                )
                print(f"✅ Script {script_name} completed:\n{result.stdout}")
            except subprocess.CalledProcessError as e:
                print(f"❌ Script {script_name} failed:\n{e.stderr}")
                return Response(
                    {"detail": f"Script {script_name} failed", "error": e.stderr},
                    status=500
                )
        else:
            print(f"❌ Script not found: {script_path}")
            return Response(
                {"detail": f"Script not found: {script_name}"},
                status=400
            )

    return Response({"detail": "Processing pipeline completed."}, status=200)



# === Health Check ===

@api_view(['GET'])
@permission_classes([AllowAny])
def health(request):
    weaviate_status = "unavailable"
    collections = []
    counts = {}

    try:
        client = weaviate.Client(url="http://localhost:8080")
        schema = client.schema.get()
        if 'classes' in schema:
            collections = [cls['class'] for cls in schema['classes']]
            for collection in collections:
                result = client.query.aggregate(collection).with_meta_count().do()
                if result and "data" in result and "Aggregate" in result["data"]:
                    count = result["data"]["Aggregate"][collection][0]["meta"]["count"]
                    counts[collection] = count
            weaviate_status = "connected"
        client.close()
    except Exception as e:
        weaviate_status = f"error: {str(e)}"

    return Response({
        "status": "healthy",
        "weaviate_status": weaviate_status,
        "collections": collections,
        "counts": counts
    })


# === Home API ===

@api_view(['GET'])
@permission_classes([AllowAny])
def home(request):
    return Response({
        "message": "AI Agent Chatbot Backend API with RAG (Django)",
        "version": "2.0",
        "framework": "Django + Django REST Framework",
        "endpoints": {
            "/api/chat/": "POST - AI-powered chat with context (RAG) [Requires Authentication]",
            "/api/signup/": "POST - Signup new user",
            "/api/token/": "POST - Login (JWT token obtain)",
            "/api/token/refresh/": "POST - Refresh JWT token",
            "/api/health/": "GET - Health check",
            "/api/upload_pdf/": "POST - Upload PDF files"
        },
        "features": [
            "JWT Authentication",
            "GPT-4o-mini powered answers",
            "Retrieval Augmented Generation (RAG)",
            "Weaviate vector database integration",
            "Technical documentation expertise"
        ]
    })


# ===== DYNAMIC FLOW MANAGEMENT =====

@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
def dynamic_flows(request):
    """
    GET: List all dynamic flows
    POST: Create a new dynamic flow and corresponding Weaviate class
    """
    from .models import DynamicFlow

    if request.method == 'GET':
        try:
            flows = DynamicFlow.objects.all().order_by('flow_name')
            flows_data = []

            for flow in flows:
                flows_data.append({
                    'id': flow.id,
                    'flow_name': flow.flow_name,
                    'flow_path': flow.flow_path,
                    'flow_path_string': flow.flow_path_string,
                    'weaviate_class_name': flow.weaviate_class_name,
                    'description': flow.description,
                    'is_active': flow.is_active,
                    'weaviate_class_created': flow.weaviate_class_created,
                    'weaviate_class_error': flow.weaviate_class_error,
                    'created_at': flow.created_at.isoformat(),
                    'created_by': flow.created_by.name if flow.created_by else None,
                })

            return JsonResponse({
                'success': True,
                'flows': flows_data
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Failed to fetch flows: {str(e)}'
            }, status=500)

    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            flow_path = data.get('flow_path', [])
            flow_name = data.get('flow_name', '')
            description = data.get('description', '')

            # Validate input
            if not flow_path or not isinstance(flow_path, list):
                return JsonResponse({
                    'success': False,
                    'error': 'flow_path is required and must be an array'
                }, status=400)

            if not flow_name:
                # Auto-generate flow name from path
                flow_name = ' > '.join(flow_path)

            # Check if flow already exists
            existing_flow = DynamicFlow.objects.filter(
                Q(flow_name=flow_name) |
                Q(flow_path=flow_path)
            ).first()

            if existing_flow:
                return JsonResponse({
                    'success': False,
                    'error': f'Flow already exists: {existing_flow.flow_name}'
                }, status=400)

            # Create the flow
            flow = DynamicFlow(
                flow_name=flow_name,
                flow_path=flow_path,
                description=description,
                created_by=request.user
            )
            flow.save()  # This will auto-generate the Weaviate class name

            # Create the Weaviate class
            success, error_msg = create_weaviate_class_for_flow(flow)

            if success:
                flow.weaviate_class_created = True
                flow.weaviate_class_error = None
            else:
                flow.weaviate_class_created = False
                flow.weaviate_class_error = error_msg

            flow.save()

            return JsonResponse({
                'success': True,
                'flow': {
                    'id': flow.id,
                    'flow_name': flow.flow_name,
                    'flow_path': flow.flow_path,
                    'flow_path_string': flow.flow_path_string,
                    'weaviate_class_name': flow.weaviate_class_name,
                    'description': flow.description,
                    'is_active': flow.is_active,
                    'weaviate_class_created': flow.weaviate_class_created,
                    'weaviate_class_error': flow.weaviate_class_error,
                    'created_at': flow.created_at.isoformat(),
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Failed to create flow: {str(e)}'
            }, status=500)


def create_weaviate_class_for_flow(flow):
    """
    Create a Weaviate class for the given dynamic flow.

    Args:
        flow (DynamicFlow): The flow object

    Returns:
        tuple: (success: bool, error_message: str or None)
    """
    try:
        # Define the schema for the new class
        schema = {
            "class": flow.weaviate_class_name,
            "description": f"Document chunks for {flow.flow_name} manuals",
            "properties": [
                {"name": "source_file", "dataType": ["string"], "description": "Filename of document"},
                {"name": "chunk_number", "dataType": ["int"], "description": "Chunk index"},
                {"name": "content", "dataType": ["text"], "description": "Text content of the chunk"},
                {"name": "camera_type", "dataType": ["string"], "description": "Camera type identifier"},
                {"name": "model_name", "dataType": ["string"], "description": "Model name for filtering"},
                {"name": "flow_path", "dataType": ["string[]"], "description": "Hierarchical flow path"},
                {"name": "created_at", "dataType": ["string"], "description": "Timestamp of creation"}
            ],
            "vectorizer": "none"
        }

        # Create the class in Weaviate
        client.schema.create_class(schema)
        print(f"✅ Created Weaviate class: {flow.weaviate_class_name}")
        return True, None

    except Exception as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            print(f"ℹ️ Class {flow.weaviate_class_name} already exists")
            return True, None  # Consider existing class as success
        else:
            print(f"❌ Error creating {flow.weaviate_class_name}: {e}")
            return False, error_msg


@api_view(['PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
def dynamic_flow_detail(request, flow_id):
    """
    PUT: Update a dynamic flow
    DELETE: Delete a dynamic flow and its Weaviate class
    """
    from .models import DynamicFlow

    try:
        flow = DynamicFlow.objects.get(id=flow_id)
    except DynamicFlow.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Flow not found'
        }, status=404)

    if request.method == 'PUT':
        try:
            data = json.loads(request.body)

            # Update fields
            if 'flow_name' in data:
                flow.flow_name = data['flow_name']
            if 'description' in data:
                flow.description = data['description']
            if 'is_active' in data:
                flow.is_active = data['is_active']

            flow.save()

            return JsonResponse({
                'success': True,
                'flow': {
                    'id': flow.id,
                    'flow_name': flow.flow_name,
                    'flow_path': flow.flow_path,
                    'flow_path_string': flow.flow_path_string,
                    'weaviate_class_name': flow.weaviate_class_name,
                    'description': flow.description,
                    'is_active': flow.is_active,
                    'weaviate_class_created': flow.weaviate_class_created,
                    'weaviate_class_error': flow.weaviate_class_error,
                }
            })

        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Failed to update flow: {str(e)}'
            }, status=500)

    elif request.method == 'DELETE':
        try:
            weaviate_class_name = flow.weaviate_class_name

            # Delete the Weaviate class
            try:
                client.schema.delete_class(weaviate_class_name)
                print(f"✅ Deleted Weaviate class: {weaviate_class_name}")
            except Exception as e:
                print(f"⚠️ Warning: Could not delete Weaviate class {weaviate_class_name}: {e}")

            # Delete the flow record
            flow.delete()

            return JsonResponse({
                'success': True,
                'message': f'Flow {flow.flow_name} deleted successfully'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'Failed to delete flow: {str(e)}'
            }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_available_flows(request):
    """
    Get all active flows for frontend dropdown population.
    """
    from .models import DynamicFlow

    try:
        flows = DynamicFlow.objects.filter(
            is_active=True,
            weaviate_class_created=True
        ).order_by('flow_name')

        flows_data = []
        for flow in flows:
            flows_data.append({
                'id': flow.id,
                'flow_name': flow.flow_name,
                'flow_path': flow.flow_path,
                'weaviate_class_name': flow.weaviate_class_name,
                'description': flow.description,
            })

        return JsonResponse({
            'success': True,
            'flows': flows_data
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Failed to fetch available flows: {str(e)}'
        }, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_hierarchy_with_flow(request):
    """
    Update the backend hierarchy JSON when new flow elements are created.
    This ensures the hierarchy stays in sync with dynamic flows.
    """
    try:
        flow_path = request.data.get('flow_path', [])
        if not flow_path:
            return JsonResponse({
                'success': False,
                'error': 'Flow path is required'
            }, status=400)

        # Get current hierarchy
        from .views import get_product_hierarchy
        hierarchy_response = get_product_hierarchy(request)
        if hierarchy_response.status_code != 200:
            return JsonResponse({
                'success': False,
                'error': 'Failed to fetch current hierarchy'
            }, status=500)

        hierarchy_data = json.loads(hierarchy_response.content)
        hierarchy = hierarchy_data.get('hierarchy', {})

        # Navigate and create path in hierarchy
        current_level = hierarchy
        for i, path_element in enumerate(flow_path):
            if path_element not in current_level:
                # Create new element in hierarchy
                if i == len(flow_path) - 1:
                    # Last element - create as leaf with models array
                    current_level[path_element] = {
                        'models': [],
                        '_isDynamicFlow': True
                    }
                else:
                    # Intermediate element - create as branch
                    current_level[path_element] = {}

            # Move to next level
            if i < len(flow_path) - 1:
                current_level = current_level[path_element]

        # Here you would typically save the updated hierarchy back to your data source
        # For now, we'll just return success since the hierarchy is dynamically generated

        return JsonResponse({
            'success': True,
            'message': 'Hierarchy updated successfully',
            'updated_hierarchy': hierarchy
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Failed to update hierarchy: {str(e)}'
        }, status=500)