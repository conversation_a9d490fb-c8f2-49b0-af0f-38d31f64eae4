import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { BACKEND_URL } from './utils/api';

export default function DynamicFlowManager() {
  const navigate = useNavigate();
  const [flows, setFlows] = useState([]);
  const [hierarchy, setHierarchy] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [creating, setCreating] = useState(false);

  // Form state with hierarchy-aware path building
  const [flowPath, setFlowPath] = useState(['']);
  const [currentPathIndex, setCurrentPathIndex] = useState(0);
  const [availableOptions, setAvailableOptions] = useState([]);
  const [flowName, setFlowName] = useState('');
  const [description, setDescription] = useState('');
  const [customInputs, setCustomInputs] = useState({}); // For "Add New" custom inputs

  useEffect(() => {
    fetchFlows();
    fetchHierarchy();
  }, []);

  const fetchFlows = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${BACKEND_URL}/api/dynamic_flows/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access')}`,
        },
      });

      const data = await response.json();
      if (response.ok) {
        setFlows(data.flows);
      } else {
        setError(data.error || 'Failed to fetch flows');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchHierarchy = async () => {
    try {
      const response = await fetch(`${BACKEND_URL}/api/product_hierarchy/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setHierarchy(data.hierarchy || {});
        // Initialize available options with top-level hierarchy
        setAvailableOptions(Object.keys(data.hierarchy || {}));
      } else {
        console.error('Failed to fetch hierarchy');
      }
    } catch (err) {
      console.error('Network error fetching hierarchy:', err.message);
    }
  };

  // Get available options for current path level
  const getOptionsForLevel = (level) => {
    let currentLevel = hierarchy;

    // Navigate to the current level in hierarchy
    for (let i = 0; i < level; i++) {
      if (flowPath[i] && currentLevel[flowPath[i]]) {
        currentLevel = currentLevel[flowPath[i]];
      } else {
        return []; // Path doesn't exist in hierarchy
      }
    }

    if (typeof currentLevel === 'object' && !Array.isArray(currentLevel)) {
      return Object.keys(currentLevel).filter(key =>
        !['models', 'versions', 'interfaces', 'platforms', 'types', 'lengths', 'focal_lengths', 'colors'].includes(key) &&
        !key.startsWith('_')
      );
    }

    return [];
  };

  // Handle selection from dropdown or custom input
  const handlePathSelection = (level, value, isCustom = false) => {
    const newPath = [...flowPath];

    if (isCustom) {
      // Use custom input value
      const customValue = customInputs[level] || '';
      if (customValue.trim()) {
        newPath[level] = customValue.trim();
        setCustomInputs(prev => ({ ...prev, [level]: '' })); // Clear custom input
      }
    } else {
      // Use selected value from dropdown
      newPath[level] = value;
    }

    // Trim path to current level + 1
    newPath.length = level + 1;
    setFlowPath(newPath);

    // Auto-generate flow name
    if (newPath.length > 0 && newPath.every(p => p)) {
      setFlowName(newPath.join(' > '));
    }
  };

  // Add new path level
  const addPathLevel = () => {
    setFlowPath(prev => [...prev, '']);
  };

  // Remove path level
  const removePathLevel = (index) => {
    if (flowPath.length > 1) {
      const newPath = flowPath.filter((_, i) => i !== index);
      setFlowPath(newPath);

      // Update flow name
      if (newPath.length > 0 && newPath.every(p => p)) {
        setFlowName(newPath.join(' > '));
      }
    }
  };

  // Reset form
  const resetForm = () => {
    setFlowPath(['']);
    setFlowName('');
    setDescription('');
    setCustomInputs({});
    setCurrentPathIndex(0);
    setShowCreateForm(false);
  };

  const handleCreateFlow = async () => {
    // Validate input
    const filteredPath = flowPath.filter(item => item && item.trim() !== '');
    if (filteredPath.length === 0) {
      setError('Please add at least one flow path element');
      return;
    }

    try {
      setCreating(true);
      setError('');

      const response = await fetch(`${BACKEND_URL}/api/dynamic_flows/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access')}`,
        },
        body: JSON.stringify({
          flow_path: filteredPath,
          flow_name: flowName || filteredPath.join(' > '),
          description: description
        }),
      });

      const data = await response.json();
      if (response.ok) {
        setFlows([...flows, data.flow]);

        // Update hierarchy with new elements
        try {
          await fetch(`${BACKEND_URL}/api/update_hierarchy/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('access')}`,
            },
            body: JSON.stringify({
              flow_path: filteredPath
            }),
          });
        } catch (err) {
          console.warn('Failed to update hierarchy:', err.message);
        }

        resetForm();
        // Refresh hierarchy to include any new elements
        fetchHierarchy();
      } else {
        setError(data.error || 'Failed to create flow');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteFlow = async (flowId, flowName) => {
    if (!window.confirm(`Are you sure you want to delete the flow "${flowName}"? This will also delete the corresponding Weaviate class.`)) {
      return;
    }

    try {
      const response = await fetch(`${BACKEND_URL}/api/dynamic_flows/${flowId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access')}`,
        },
      });

      const data = await response.json();
      if (response.ok) {
        setFlows(flows.filter(flow => flow.id !== flowId));
      } else {
        setError(data.error || 'Failed to delete flow');
      }
    } catch (err) {
      setError('Network error: ' + err.message);
    }
  };

  const addPathElement = () => {
    setNewFlow({
      ...newFlow,
      flow_path: [...newFlow.flow_path, '']
    });
  };

  const updatePathElement = (index, value) => {
    const updatedPath = [...newFlow.flow_path];
    updatedPath[index] = value;
    setNewFlow({
      ...newFlow,
      flow_path: updatedPath
    });
  };

  const removePathElement = (index) => {
    const updatedPath = newFlow.flow_path.filter((_, i) => i !== index);
    setNewFlow({
      ...newFlow,
      flow_path: updatedPath.length > 0 ? updatedPath : ['']
    });
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(to bottom right, #1E3A8A, #3B82F6)',
      padding: '40px 20px'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '1.5rem',
        padding: '2rem',
        boxShadow: '0 20px 25px rgba(0, 0, 0, 0.25)',
        backdropFilter: 'blur(10px)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '2rem'
        }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: '600',
            color: '#1E3A8A',
            margin: 0
          }}>
            Dynamic Flow Manager
          </h1>
          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={() => setShowCreateForm(!showCreateForm)}
              style={{
                padding: '12px 24px',
                backgroundColor: '#10B981',
                color: 'white',
                border: 'none',
                borderRadius: '0.75rem',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#059669'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#10B981'}
            >
              {showCreateForm ? 'Cancel' : 'Create New Flow'}
            </button>
            <button
              onClick={() => navigate('/admin')}
              style={{
                padding: '12px 24px',
                backgroundColor: '#6B7280',
                color: 'white',
                border: 'none',
                borderRadius: '0.75rem',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease-in-out'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = '#4B5563'}
              onMouseOut={(e) => e.target.style.backgroundColor = '#6B7280'}
            >
              Back to Admin
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div style={{
            backgroundColor: '#FEE2E2',
            border: '1px solid #FECACA',
            color: '#DC2626',
            padding: '12px',
            borderRadius: '0.5rem',
            marginBottom: '1rem'
          }}>
            {error}
          </div>
        )}

        {/* Create Form */}
        {showCreateForm && (
          <div style={{
            backgroundColor: '#F8FAFC',
            border: '2px solid #E2E8F0',
            borderRadius: '1rem',
            padding: '1.5rem',
            marginBottom: '2rem'
          }}>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: '#1E3A8A',
              marginBottom: '1rem'
            }}>
              Create New Flow
            </h2>

            {/* Hierarchy-Aware Flow Path Builder */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Flow Path * (Select from existing hierarchy or add new)
              </label>

              {/* Current Path Display */}
              {flowPath.length > 0 && flowPath[0] && (
                <div style={{
                  backgroundColor: '#EBF8FF',
                  border: '1px solid #BEE3F8',
                  borderRadius: '0.5rem',
                  padding: '0.75rem',
                  marginBottom: '1rem'
                }}>
                  <strong>Current Path:</strong> {flowPath.filter(p => p).join(' > ')}
                </div>
              )}

              {/* Path Level Builders */}
              {flowPath.map((element, index) => {
                const availableOptions = getOptionsForLevel(index);
                return (
                  <div key={index} style={{
                    border: '1px solid #E5E7EB',
                    borderRadius: '0.5rem',
                    padding: '1rem',
                    marginBottom: '0.75rem',
                    backgroundColor: '#FAFAFA'
                  }}>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      <label style={{
                        fontWeight: '600',
                        color: '#374151'
                      }}>
                        Level {index + 1}
                      </label>
                      {flowPath.length > 1 && (
                        <button
                          onClick={() => removePathLevel(index)}
                          style={{
                            padding: '0.25rem 0.5rem',
                            backgroundColor: '#EF4444',
                            color: 'white',
                            border: 'none',
                            borderRadius: '0.25rem',
                            fontSize: '0.75rem',
                            cursor: 'pointer'
                          }}
                        >
                          Remove Level
                        </button>
                      )}
                    </div>

                    {/* Dropdown for existing options */}
                    <select
                      value={element}
                      onChange={(e) => handlePathSelection(index, e.target.value)}
                      style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '2px solid #E5E7EB',
                        borderRadius: '0.5rem',
                        fontSize: '1rem',
                        marginBottom: '0.5rem'
                      }}
                    >
                      <option value="">-- Select from existing hierarchy --</option>
                      {availableOptions.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>

                    {/* Custom input option */}
                    <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                      <input
                        type="text"
                        value={customInputs[index] || ''}
                        onChange={(e) => setCustomInputs(prev => ({ ...prev, [index]: e.target.value }))}
                        placeholder="Or enter new option..."
                        style={{
                          flex: 1,
                          padding: '0.75rem',
                          border: '2px solid #FCD34D',
                          borderRadius: '0.5rem',
                          fontSize: '1rem'
                        }}
                      />
                      <button
                        onClick={() => handlePathSelection(index, '', true)}
                        disabled={!customInputs[index]?.trim()}
                        style={{
                          padding: '0.75rem 1rem',
                          backgroundColor: customInputs[index]?.trim() ? '#F59E0B' : '#D1D5DB',
                          color: 'white',
                          border: 'none',
                          borderRadius: '0.5rem',
                          fontSize: '0.875rem',
                          cursor: customInputs[index]?.trim() ? 'pointer' : 'not-allowed'
                        }}
                      >
                        Add New
                      </button>
                    </div>
                  </div>
                );
              })}

              {/* Add Level Button */}
              <button
                onClick={addPathLevel}
                style={{
                  padding: '0.75rem 1rem',
                  backgroundColor: '#3B82F6',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.5rem',
                  fontSize: '0.875rem',
                  cursor: 'pointer'
                }}
              >
                Add Next Level
              </button>
            </div>

            {/* Flow Name */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Flow Name (optional - auto-generated if empty)
              </label>
              <input
                type="text"
                value={flowName}
                onChange={(e) => setFlowName(e.target.value)}
                placeholder="e.g., Linea GigE Monochrome"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #E5E7EB',
                  borderRadius: '0.5rem',
                  fontSize: '1rem'
                }}
              />
            </div>

            {/* Description */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{
                display: 'block',
                fontWeight: '600',
                color: '#374151',
                marginBottom: '0.5rem'
              }}>
                Description (optional)
              </label>
              <textarea
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Description of this flow..."
                rows={3}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: '2px solid #E5E7EB',
                  borderRadius: '0.5rem',
                  fontSize: '1rem',
                  resize: 'vertical'
                }}
              />
            </div>

            {/* Create Button */}
            <button
              onClick={handleCreateFlow}
              disabled={creating}
              style={{
                padding: '0.75rem 1.5rem',
                backgroundColor: creating ? '#9CA3AF' : '#10B981',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: creating ? 'not-allowed' : 'pointer'
              }}
            >
              {creating ? 'Creating...' : 'Create Flow'}
            </button>
          </div>
        )}

        {/* Flows List */}
        <div>
          <h2 style={{
            fontSize: '1.5rem',
            fontWeight: '600',
            color: '#1E3A8A',
            marginBottom: '1rem'
          }}>
            Existing Flows
          </h2>

          {loading ? (
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              color: '#6B7280'
            }}>
              Loading flows...
            </div>
          ) : flows.length === 0 ? (
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              color: '#6B7280',
              backgroundColor: '#F9FAFB',
              borderRadius: '0.5rem',
              border: '2px dashed #D1D5DB'
            }}>
              No flows created yet. Create your first flow to get started!
            </div>
          ) : (
            <div style={{
              display: 'grid',
              gap: '1rem'
            }}>
              {flows.map((flow) => (
                <div
                  key={flow.id}
                  style={{
                    backgroundColor: 'white',
                    border: '2px solid #E5E7EB',
                    borderRadius: '0.75rem',
                    padding: '1.5rem',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'flex-start',
                    marginBottom: '1rem'
                  }}>
                    <div style={{ flex: 1 }}>
                      <h3 style={{
                        fontSize: '1.25rem',
                        fontWeight: '600',
                        color: '#1F2937',
                        margin: '0 0 0.5rem 0'
                      }}>
                        {flow.flow_name}
                      </h3>
                      <p style={{
                        color: '#6B7280',
                        margin: '0 0 0.5rem 0',
                        fontSize: '0.875rem'
                      }}>
                        Path: {flow.flow_path_string}
                      </p>
                      <p style={{
                        color: '#6B7280',
                        margin: '0 0 0.5rem 0',
                        fontSize: '0.875rem'
                      }}>
                        Weaviate Class: <code style={{
                          backgroundColor: '#F3F4F6',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '0.25rem',
                          fontFamily: 'monospace'
                        }}>{flow.weaviate_class_name}</code>
                      </p>
                      {flow.description && (
                        <p style={{
                          color: '#4B5563',
                          margin: '0.5rem 0 0 0',
                          fontSize: '0.875rem'
                        }}>
                          {flow.description}
                        </p>
                      )}
                    </div>
                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-end',
                      gap: '0.5rem'
                    }}>
                      <div style={{
                        display: 'flex',
                        gap: '0.5rem',
                        alignItems: 'center'
                      }}>
                        <span style={{
                          padding: '0.25rem 0.75rem',
                          borderRadius: '1rem',
                          fontSize: '0.75rem',
                          fontWeight: '600',
                          backgroundColor: flow.is_active ? '#D1FAE5' : '#FEE2E2',
                          color: flow.is_active ? '#065F46' : '#991B1B'
                        }}>
                          {flow.is_active ? 'Active' : 'Inactive'}
                        </span>
                        <span style={{
                          padding: '0.25rem 0.75rem',
                          borderRadius: '1rem',
                          fontSize: '0.75rem',
                          fontWeight: '600',
                          backgroundColor: flow.weaviate_class_created ? '#DBEAFE' : '#FEF3C7',
                          color: flow.weaviate_class_created ? '#1E40AF' : '#92400E'
                        }}>
                          {flow.weaviate_class_created ? 'Class Created' : 'Class Pending'}
                        </span>
                      </div>
                      <button
                        onClick={() => handleDeleteFlow(flow.id, flow.flow_name)}
                        style={{
                          padding: '0.5rem 1rem',
                          backgroundColor: '#EF4444',
                          color: 'white',
                          border: 'none',
                          borderRadius: '0.5rem',
                          fontSize: '0.875rem',
                          fontWeight: '600',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease-in-out'
                        }}
                        onMouseOver={(e) => e.target.style.backgroundColor = '#DC2626'}
                        onMouseOut={(e) => e.target.style.backgroundColor = '#EF4444'}
                      >
                        Delete
                      </button>
                    </div>
                  </div>

                  {flow.weaviate_class_error && (
                    <div style={{
                      backgroundColor: '#FEE2E2',
                      border: '1px solid #FECACA',
                      color: '#DC2626',
                      padding: '0.75rem',
                      borderRadius: '0.5rem',
                      fontSize: '0.875rem'
                    }}>
                      <strong>Error:</strong> {flow.weaviate_class_error}
                    </div>
                  )}

                  <div style={{
                    fontSize: '0.75rem',
                    color: '#9CA3AF',
                    marginTop: '1rem',
                    paddingTop: '1rem',
                    borderTop: '1px solid #E5E7EB'
                  }}>
                    Created: {new Date(flow.created_at).toLocaleString()}
                    {flow.created_by && ` by ${flow.created_by}`}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
