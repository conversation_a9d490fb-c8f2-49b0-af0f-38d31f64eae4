import React from "react";
import { useNavigate, useLocation } from "react-router-dom";

export default function LineaCameraTypeSelection({ token }) {
  const navigate = useNavigate();
  const location = useLocation();

  // Get the interface type from location state or sessionStorage
  const interfaceType = location.state?.interfaceType || sessionStorage.getItem('lineaInterfaceType');

  React.useEffect(() => {
    // If no interface type is available, redirect back
    if (!interfaceType) {
      navigate('/hierarchical-selection');
    }
  }, [interfaceType, navigate]);

  const handleTypeSelection = (cameraType) => {
    // Store the camera type selection
    sessionStorage.setItem('lineaCameraType', cameraType);

    // Get the selected brand from sessionStorage
    const selectedBrand = sessionStorage.getItem('selectedBrand') || '';

    // Create hierarchical selection data for the ticket details form
    const hierarchicalData = {
      productCategory: 'Camera',
      productSubcategory: 'Line Scan',
      productFamily: 'Linea',
      productInterface: interfaceType,
      productColorType: cameraType,
      path: ['Camera', 'Line Scan', 'Linea', interfaceType, cameraType],
      // Flag to indicate this is a Linea camera requiring model selection
      isLineaCamera: true,
      interfaceType: interfaceType,
      cameraType: cameraType
    };

    // Store the hierarchical selection data
    sessionStorage.setItem('hierarchicalSelection', JSON.stringify(hierarchicalData));

    // Navigate directly to ticket details form
    navigate('/new-ticket-details');
  };

  const handleBack = () => {
    // Clear stored data and go back
    sessionStorage.removeItem('lineaInterfaceType');
    navigate('/hierarchical-selection');
  };

  if (!interfaceType) {
    return null; // Will redirect in useEffect
  }

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      fontFamily: "Arial, sans-serif"
    }}>
      <div style={{
        background: "rgba(255, 255, 255, 0.95)",
        borderRadius: "1.5rem",
        padding: "3rem",
        maxWidth: "800px",
        width: "100%",
        boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
        backdropFilter: "blur(10px)"
      }}>
        <h1 style={{
          color: "#1E3A8A",
          marginBottom: "1.5rem",
          textAlign: "center",
          fontSize: "2rem",
          fontWeight: "600"
        }}>
          Choose Camera Type
        </h1>

        {/* Breadcrumb */}
        <div style={{
          backgroundColor: "#DBEAFE",
          padding: "12px 16px",
          borderRadius: "0.75rem",
          marginBottom: "1.5rem",
          fontSize: "0.875rem",
          color: "#1E40AF",
          border: "2px solid #BFDBFE"
        }}>
          <strong>Selected:</strong> Line Scan &gt; Linea &gt; {interfaceType}
        </div>

        <p style={{
          fontSize: "1.2rem",
          color: "#4B5563",
          marginBottom: "2rem",
          textAlign: "center",
          lineHeight: "1.6"
        }}>
          Please select your camera type:
        </p>

        {/* Camera Type Options */}
        <div style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          marginBottom: "2rem"
        }}>
          <button
            onClick={() => handleTypeSelection("Monochrome")}
            style={{
              padding: "16px 20px",
              border: "2px solid #E5E7EB",
              borderRadius: "0.75rem",
              backgroundColor: "#ffffff",
              cursor: "pointer",
              fontSize: "1rem",
              fontWeight: "600",
              color: "#1E3A8A",
              transition: "all 0.2s ease-in-out",
              textAlign: "center",
              minHeight: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.borderColor = "#3B82F6";
              e.target.style.backgroundColor = "#EFF6FF";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.borderColor = "#E5E7EB";
              e.target.style.backgroundColor = "#ffffff";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            Monochrome
          </button>

          <button
            onClick={() => handleTypeSelection("Color")}
            style={{
              padding: "16px 20px",
              border: "2px solid #E5E7EB",
              borderRadius: "0.75rem",
              backgroundColor: "#ffffff",
              cursor: "pointer",
              fontSize: "1rem",
              fontWeight: "600",
              color: "#1E3A8A",
              transition: "all 0.2s ease-in-out",
              textAlign: "center",
              minHeight: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.borderColor = "#3B82F6";
              e.target.style.backgroundColor = "#EFF6FF";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.borderColor = "#E5E7EB";
              e.target.style.backgroundColor = "#ffffff";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            Color
          </button>
        </div>

        {/* Navigation Buttons */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        }}>
          <button
            onClick={handleBack}
            style={{
              padding: "12px 24px",
              backgroundColor: "#6B7280",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              fontWeight: "600",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#4B5563";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#6B7280";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            ← Back
          </button>

          <div style={{
            fontSize: "0.875rem",
            color: "#4B5563",
            textAlign: "center",
            fontWeight: "600"
          }}>
            Step 4 of 5
          </div>
        </div>
      </div>
    </div>
  );
}
