import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";

export default function LineaModelSelection({ token }) {
  const navigate = useNavigate();
  const location = useLocation();

  // Get the interface type and camera type from location state or sessionStorage
  const interfaceType = location.state?.interfaceType || sessionStorage.getItem('lineaInterfaceType');
  const cameraType = location.state?.cameraType || sessionStorage.getItem('lineaCameraType');

  const [selectedModel, setSelectedModel] = useState("");
  const [selectedFramegrabber, setSelectedFramegrabber] = useState("");
  const [error, setError] = useState("");

  // Model options based on interface type and camera type
  const getModelOptions = () => {
    if (interfaceType === "GigE") {
      if (cameraType === "Monochrome") {
        return ["LA-GM-02K08A", "LA-GM-04K08A", "LA-GM-08K08A"];
      } else if (cameraType === "Color") {
        return ["LA-GC-02K08A", "LA-GC-04K08A"];
      }
    } else if (interfaceType === "Camera Link") {
      if (cameraType === "Monochrome") {
        return ["LA-CM-02K08A", "LA-CM-04K08A", "LA-CM-08K08A", "LA-CM-16K05A"];
      } else if (cameraType === "Color") {
        return ["LA-CC-04K05B", "LA-CC-08K05B"];
      }
    }
    return [];
  };

  const framegrabberOptions = ["xtium cl mx4", "xtium 2 cl mx4"];
  const modelOptions = getModelOptions();
  const isCameraLink = interfaceType === "Camera Link";

  useEffect(() => {
    // If no interface type or camera type is available, redirect back
    if (!interfaceType || !cameraType) {
      navigate('/hierarchical-selection');
    }
  }, [interfaceType, cameraType, navigate]);

  const handleSubmit = () => {
    // Validate model selection
    if (!selectedModel) {
      setError("Please select a model");
      return;
    }

    // Validate framegrabber selection for Camera Link
    if (isCameraLink && !selectedFramegrabber) {
      setError("Please select a frame grabber");
      return;
    }

    // Store the complete hierarchical selection
    const hierarchicalData = {
      path: ['Camera', 'Line Scan', 'Linea', interfaceType, cameraType],
      leafData: {
        models: modelOptions
      },
      productCategory: 'Camera',
      productSubcategory: 'Line Scan',
      productFamily: 'Linea',
      productInterface: interfaceType,
      productColorType: cameraType,
      selectedModel: selectedModel,
      framegrabber: isCameraLink ? selectedFramegrabber : null,
      isLineaCamera: true
    };

    sessionStorage.setItem('hierarchicalSelection', JSON.stringify(hierarchicalData));
    
    // Clear temporary storage
    sessionStorage.removeItem('lineaInterfaceType');
    sessionStorage.removeItem('lineaCameraType');
    
    // Navigate to details form
    navigate('/new-ticket-details');
  };

  const handleBack = () => {
    navigate('/linea-camera-type', {
      state: {
        interfaceType: interfaceType
      }
    });
  };

  if (!interfaceType || !cameraType) {
    return null; // Will redirect in useEffect
  }

  return (
    <div style={{
      minHeight: "100vh",
      background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      padding: "2rem",
      fontFamily: "Arial, sans-serif"
    }}>
      <div style={{
        background: "rgba(255, 255, 255, 0.95)",
        borderRadius: "1.5rem",
        padding: "3rem",
        maxWidth: "800px",
        width: "100%",
        boxShadow: "0 20px 25px rgba(0, 0, 0, 0.25)",
        backdropFilter: "blur(10px)"
      }}>
        <h1 style={{
          color: "#1E3A8A",
          marginBottom: "1.5rem",
          textAlign: "center",
          fontSize: "2rem",
          fontWeight: "600"
        }}>
          Select Model
        </h1>

        {/* Breadcrumb */}
        <div style={{
          backgroundColor: "#DBEAFE",
          padding: "12px 16px",
          borderRadius: "0.75rem",
          marginBottom: "1.5rem",
          fontSize: "0.875rem",
          color: "#1E40AF",
          border: "2px solid #BFDBFE"
        }}>
          <strong>Selected:</strong> Line Scan &gt; Linea &gt; {interfaceType} &gt; {cameraType}
        </div>

        {error && (
          <div style={{
            backgroundColor: "#FEE2E2",
            color: "#DC2626",
            padding: "12px 16px",
            borderRadius: "0.75rem",
            marginBottom: "1.5rem",
            border: "2px solid #FECACA",
            textAlign: "center"
          }}>
            {error}
          </div>
        )}

        <p style={{
          fontSize: "1.2rem",
          color: "#4B5563",
          marginBottom: "2rem",
          textAlign: "center",
          lineHeight: "1.6"
        }}>
          Please select your camera model{isCameraLink ? " and frame grabber" : ""}:
        </p>

        {/* Model Selection Dropdown */}
        <div style={{ marginBottom: "1.5rem" }}>
          <label style={{
            display: "block",
            marginBottom: "8px",
            fontWeight: "600",
            color: "#1E3A8A",
            fontSize: "1rem"
          }}>
            Model *
          </label>
          <select
            value={selectedModel}
            onChange={(e) => {
              setSelectedModel(e.target.value);
              setError("");
            }}
            style={{
              width: "100%",
              padding: "12px 16px",
              border: "2px solid #E5E7EB",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              backgroundColor: "#ffffff",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out",
              outline: "none"
            }}
            onFocus={(e) => {
              e.target.style.borderColor = "#3B82F6";
            }}
            onBlur={(e) => {
              e.target.style.borderColor = "#E5E7EB";
            }}
          >
            <option value="">Select a model</option>
            {modelOptions.map((model) => (
              <option key={model} value={model}>
                {model}
              </option>
            ))}
          </select>
        </div>

        {/* Frame Grabber Selection (only for Camera Link) */}
        {isCameraLink && (
          <div style={{ marginBottom: "2rem" }}>
            <label style={{
              display: "block",
              marginBottom: "8px",
              fontWeight: "600",
              color: "#1E3A8A",
              fontSize: "1rem"
            }}>
              Frame Grabber *
            </label>
            <select
              value={selectedFramegrabber}
              onChange={(e) => {
                setSelectedFramegrabber(e.target.value);
                setError("");
              }}
              style={{
                width: "100%",
                padding: "12px 16px",
                border: "2px solid #E5E7EB",
                borderRadius: "0.75rem",
                fontSize: "1rem",
                backgroundColor: "#ffffff",
                cursor: "pointer",
                transition: "all 0.2s ease-in-out",
                outline: "none"
              }}
              onFocus={(e) => {
                e.target.style.borderColor = "#3B82F6";
              }}
              onBlur={(e) => {
                e.target.style.borderColor = "#E5E7EB";
              }}
            >
              <option value="">Select a frame grabber</option>
              {framegrabberOptions.map((fg) => (
                <option key={fg} value={fg}>
                  {fg}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Navigation Buttons */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginTop: "2rem"
        }}>
          <button
            onClick={handleBack}
            style={{
              padding: "12px 24px",
              backgroundColor: "#6B7280",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              fontWeight: "600",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#4B5563";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#6B7280";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            ← Back
          </button>

          <button
            onClick={handleSubmit}
            style={{
              padding: "12px 24px",
              backgroundColor: "#2563EB",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              fontWeight: "600",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
            }}
            onMouseOver={(e) => {
              e.target.style.backgroundColor = "#1E40AF";
              e.target.style.transform = "translateY(-1px)";
              e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
            }}
            onMouseOut={(e) => {
              e.target.style.backgroundColor = "#2563EB";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
            }}
          >
            Continue →
          </button>
        </div>
      </div>
    </div>
  );
}
