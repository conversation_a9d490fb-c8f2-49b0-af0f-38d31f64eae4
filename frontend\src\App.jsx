import React, { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Link,
  Navigate,
} from "react-router-dom";

import Home from "./Home.jsx";
import Uploads from "./upload.jsx";
import AuthForm from "./AuthForm.jsx";
import AdminPromptPage from "./AdminPromptPage.jsx";
import AdminDashboard from "./AdminDashboard.jsx";
import EscalatedTicketsPage from "./EscalatedTicketsPage.jsx";
import AdminChatbot from "./AdminChatbot.jsx";
import ActionsPage from "./ActionsPage.jsx";
import NewTicketForm from "./NewTicketForm.jsx";
import BrandSelectionPage from "./BrandSelectionPage.jsx";
import HierarchicalProductSelection from "./HierarchicalProductSelection.jsx";
import NewTicketDetailsForm from "./NewTicketDetailsForm.jsx";
import SelectTicketPage from "./SelectTicketPage.jsx";
import StructuredChatbot from "./StructuredChatbot.jsx";
import Footer from "./components/Footer.jsx";
import ProblemCategoriesPage from "./ProblemCategoriesPage.jsx";
import ProblemDescriptionPage from "./ProblemDescriptionPage.jsx";
import PendingTicketsList from "./PendingTicketsList.jsx";
import UsageDashboard from "./UsageDashboard.jsx";
import LineaCameraTypeSelection from "./LineaCameraTypeSelection.jsx";
import LineaModelSelection from "./LineaModelSelection.jsx";
import DynamicFlowManager from "./DynamicFlowManager.jsx";
import EnhancedProductSelection from "./EnhancedProductSelection.jsx";

function App() {
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Check for existing authentication data on app startup
    const userData = localStorage.getItem("userData");
    const accessToken = localStorage.getItem("access");

    if (userData && accessToken) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        console.log("User authenticated from localStorage:", parsedUser.email);
      } catch (error) {
        console.error("Error parsing user data:", error);
        // Clear corrupted data
        localStorage.removeItem("userData");
        localStorage.removeItem("access");
        localStorage.removeItem("refresh");
        setUser(null);
      }
    } else {
      console.log("No authentication data found. User will need to login.");
      setUser(null);
    }

    // Add body class for footer styling
    document.body.classList.add('has-footer');

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('has-footer');
    };
  }, []);

  const handleLoginSuccess = (userData, tokens) => {
    setUser(userData);
    localStorage.setItem("userData", JSON.stringify(userData));
    localStorage.setItem("access", tokens.access);
    localStorage.setItem("refresh", tokens.refresh);
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");
  };

  const PrivateRoute = ({ children }) => (user ? children : <Navigate to="/auth" replace />);
  const AdminRoute = ({ children }) =>
    user && user.is_admin ? children : <Navigate to="/" replace />;

  return (
    <Router>
      {window.location.pathname !== "/admin" && (
        <nav style={{
          background: "linear-gradient(to bottom right, #1E3A8A, #3B82F6)",
          padding: "16px 24px",
          display: "flex",
          justifyContent: "flex-end",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
        }}>
          <div>
            {user ? (
              <button
                onClick={handleLogout}
                className="blue-button"
                style={{
                  background: "#2563EB",
                  color: "white",
                  border: "none",
                  padding: "8px 16px",
                  borderRadius: "0.75rem",
                  fontWeight: "600",
                  cursor: "pointer",
                  transition: "all 0.2s ease-in-out",
                  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)"
                }}
                onMouseOver={(e) => {
                  e.target.style.background = "#1E40AF";
                  e.target.style.transform = "translateY(-1px)";
                  e.target.style.boxShadow = "0 6px 12px rgba(0, 0, 0, 0.15)";
                }}
                onMouseOut={(e) => {
                  e.target.style.background = "#2563EB";
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
                }}
              >
                Logout
              </button>
            ) : (
              <>
                <Link to="/auth" style={{
                  marginRight: 16,
                  color: "white",
                  textDecoration: "none",
                  fontWeight: "600"
                }}>
                  Login
                </Link>
                <Link to="/signup" style={{
                  color: "white",
                  textDecoration: "none",
                  fontWeight: "600"
                }}>
                  Sign Up
                </Link>
              </>
            )}
          </div>
        </nav>
      )}

      <Routes>
        <Route
          path="/"
          element={
            user ? (user.is_admin ? <Navigate to="/admin" replace /> : <Navigate to="/actions" replace />) : <Navigate to="/auth" replace />
          }
        />

        <Route
          path="/actions"
          element={
            <PrivateRoute>
              <ActionsPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/new-ticket"
          element={
            <PrivateRoute>
              <NewTicketForm token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/select-brand"
          element={
            <PrivateRoute>
              <BrandSelectionPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/hierarchical-selection"
          element={
            <PrivateRoute>
              <HierarchicalProductSelection token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/enhanced-selection"
          element={
            <PrivateRoute>
              <EnhancedProductSelection token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/hierarchical-selection-legacy"
          element={
            <PrivateRoute>
              <HierarchicalProductSelection token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/new-ticket-details"
          element={
            <PrivateRoute>
              <NewTicketDetailsForm token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/linea-camera-type"
          element={
            <PrivateRoute>
              <LineaCameraTypeSelection token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/linea-model-selection"
          element={
            <PrivateRoute>
              <LineaModelSelection token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/pending-tickets"
          element={
            <PrivateRoute>
              <PendingTicketsList />
            </PrivateRoute>
          }
        />

        <Route
          path="/select-ticket"
          element={
            <PrivateRoute>
              <SelectTicketPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/problem-categories"
          element={
            <PrivateRoute>
              <ProblemCategoriesPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/problem-description"
          element={
            <PrivateRoute>
              <ProblemDescriptionPage token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/chatbot/:ticketId"
          element={
            <PrivateRoute>
              <Home token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/legacy-chat"
          element={
            <PrivateRoute>
              <Home token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/structured-chat/:ticketId"
          element={
            <PrivateRoute>
              <StructuredChatbot token={localStorage.getItem("access")} />
            </PrivateRoute>
          }
        />

        <Route
          path="/uploads"
          element={
            <PrivateRoute>
              <Uploads />
            </PrivateRoute>
          }
        />

        <Route
          path="/prompt-manager"
          element={
            <AdminRoute>
              <AdminPromptPage />
            </AdminRoute>
          }
        />

        <Route
          path="/admin"
          element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          }
        />

        <Route
          path="/admin/tickets"
          element={
            <AdminRoute>
              <EscalatedTicketsPage />
            </AdminRoute>
          }
        />

        <Route
          path="/auth"
          element={<AuthForm onLoginSuccess={handleLoginSuccess} />}
        />
        <Route
          path="/admin/chatbot"
          element={
            <AdminRoute>
              <AdminChatbot />
            </AdminRoute>
          }
        />

        <Route
          path="/usage"
          element={
            <AdminRoute>
              <UsageDashboard />
            </AdminRoute>
          }
        />

        <Route
          path="/dynamic-flows"
          element={
            <AdminRoute>
              <DynamicFlowManager />
            </AdminRoute>
          }
        />

        <Route
          path="/signup"
          element={<AuthForm defaultMode="signup" onLoginSuccess={handleLoginSuccess} />}
        />
      </Routes>
      <Footer />
    </Router>
  );
}

export default App;