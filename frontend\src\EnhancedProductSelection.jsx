import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useEnhancedProductHierarchy, useDynamicFlows } from "./hooks/useDynamicFlows";

export default function EnhancedProductSelection({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  
  const { hierarchy, loading, error } = useEnhancedProductHierarchy();
  const { isDynamicFlow, getWeaviateClassForPath } = useDynamicFlows();
  
  const [selectedPath, setSelectedPath] = useState([]);
  const [currentLevel, setCurrentLevel] = useState({});

  // Update current level when hierarchy changes
  useEffect(() => {
    if (hierarchy && Object.keys(hierarchy).length > 0) {
      setCurrentLevel(hierarchy);
    }
  }, [hierarchy]);

  const handleSelection = (key) => {
    const newPath = [...selectedPath, key];
    setSelectedPath(newPath);

    // Check if this is a dynamic flow
    const dynamicFlow = isDynamicFlow && isDynamicFlow(newPath);
    if (dynamicFlow) {
      // This is a complete dynamic flow path
      proceedToDetailsForm(newPath, {
        _isDynamicFlow: true,
        _flowId: dynamicFlow.id,
        _weaviateClass: dynamicFlow.weaviate_class_name,
        _description: dynamicFlow.description
      });
      return;
    }

    // Special handling for Linea cameras - redirect to custom workflow
    if (newPath.length === 3 && newPath[0] === 'Camera' && newPath[1] === 'Line Scan' && newPath[2] === 'Linea') {
      const nextLevel = currentLevel && currentLevel[key];
      if (nextLevel) {
        setCurrentLevel(nextLevel);
      }
      return;
    }

    // Handle Linea interface selection
    if (newPath.length === 4 && newPath[0] === 'Camera' && newPath[1] === 'Line Scan' && newPath[2] === 'Linea') {
      const interfaceType = key;
      sessionStorage.setItem('lineaInterfaceType', interfaceType);
      navigate(`/linea-camera-type/${interfaceType}`);
      return;
    }

    // Navigate deeper into the hierarchy
    const nextLevel = currentLevel && currentLevel[key];

    if (nextLevel && typeof nextLevel === 'object' && !Array.isArray(nextLevel)) {
      // Check if this is a leaf node (contains models, versions, etc.)
      const hasSubCategories = Object.keys(nextLevel).some(k =>
        typeof nextLevel[k] === 'object' &&
        !Array.isArray(nextLevel[k]) &&
        !['models', 'versions', 'interfaces', 'platforms', 'types', 'lengths', 'focal_lengths', 'colors', '_isDynamicFlow', '_flowId', '_weaviateClass', '_description'].includes(k)
      );

      if (hasSubCategories) {
        setCurrentLevel(nextLevel);
      } else {
        // This is a leaf node, proceed to details form
        proceedToDetailsForm(newPath, nextLevel);
      }
    } else {
      // This shouldn't happen with our hierarchy structure
      proceedToDetailsForm(newPath, {});
    }
  };

  const proceedToDetailsForm = (path, leafData) => {
    // Store the hierarchical selection in sessionStorage for the details form
    const hierarchicalData = {
      path: path,
      leafData: leafData,
      productCategory: path[0] || '',
      productSubcategory: path[1] || '',
      productFamily: path[2] || '',
      productInterface: path[3] || '',
      productColorType: path[4] || '', // Add color type for Line Scan cameras
      isDynamicFlow: leafData._isDynamicFlow || false,
      flowId: leafData._flowId || null,
      weaviateClass: leafData._weaviateClass || null,
      flowDescription: leafData._description || null
    };

    sessionStorage.setItem('hierarchicalSelection', JSON.stringify(hierarchicalData));
    navigate('/new-ticket-details');
  };

  const getBreadcrumb = () => {
    return selectedPath.join(' > ');
  };

  const goBack = () => {
    if (selectedPath.length === 0) {
      navigate('/select-brand');
      return;
    }

    const newPath = selectedPath.slice(0, -1);
    setSelectedPath(newPath);

    // Navigate back in the hierarchy
    let newLevel = hierarchy;
    for (const pathElement of newPath) {
      newLevel = newLevel[pathElement];
    }
    setCurrentLevel(newLevel);
  };

  const currentOptions = currentLevel && typeof currentLevel === 'object'
    ? Object.keys(currentLevel).filter(key =>
        !key.startsWith('_') // Filter out metadata keys
      )
    : [];

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(to bottom right, #1E3A8A, #3B82F6)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          padding: '2rem',
          borderRadius: '1rem',
          textAlign: 'center',
          color: '#1E3A8A'
        }}>
          Loading product hierarchy...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(to bottom right, #1E3A8A, #3B82F6)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          padding: '2rem',
          borderRadius: '1rem',
          textAlign: 'center'
        }}>
          <h2 style={{ color: '#DC2626', marginBottom: '1rem' }}>Error</h2>
          <p style={{ color: '#374151' }}>{error}</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              marginTop: '1rem',
              padding: '0.75rem 1.5rem',
              backgroundColor: '#3B82F6',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer'
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(to bottom right, #1E3A8A, #3B82F6)',
      padding: '40px 20px'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '1.5rem',
        padding: '2rem',
        boxShadow: '0 20px 25px rgba(0, 0, 0, 0.25)',
        backdropFilter: 'blur(10px)'
      }}>
        {/* Header */}
        <div style={{
          textAlign: 'center',
          marginBottom: '2rem'
        }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: '600',
            color: '#1E3A8A',
            marginBottom: '0.5rem'
          }}>
            Select Product
          </h1>
          <p style={{
            color: '#6B7280',
            fontSize: '1rem'
          }}>
            Choose your product category and specifications
          </p>
        </div>

        {/* Breadcrumb */}
        {selectedPath.length > 0 && (
          <div style={{
            backgroundColor: '#F3F4F6',
            padding: '1rem',
            borderRadius: '0.75rem',
            marginBottom: '1.5rem'
          }}>
            <div style={{
              fontSize: '0.875rem',
              color: '#6B7280',
              marginBottom: '0.25rem'
            }}>
              Current Selection:
            </div>
            <div style={{
              fontSize: '1rem',
              fontWeight: '600',
              color: '#1F2937'
            }}>
              {getBreadcrumb()}
            </div>
          </div>
        )}

        {/* Product Options */}
        <div style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          marginBottom: "2rem"
        }}>
          {currentOptions.map((option) => {
            const optionData = currentLevel && currentLevel[option];
            const isDynamic = optionData && optionData._isDynamicFlow;
            
            return (
              <button
                key={option}
                onClick={() => handleSelection(option)}
                style={{
                  padding: "16px 20px",
                  border: isDynamic ? "2px solid #8B5CF6" : "2px solid #E5E7EB",
                  borderRadius: "0.75rem",
                  backgroundColor: isDynamic ? "#F3E8FF" : "#ffffff",
                  cursor: "pointer",
                  fontSize: "1rem",
                  fontWeight: "600",
                  color: isDynamic ? "#7C3AED" : "#1E3A8A",
                  transition: "all 0.2s ease-in-out",
                  textAlign: "center",
                  minHeight: "80px",
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                  position: "relative"
                }}
                onMouseOver={(e) => {
                  e.target.style.transform = "translateY(-2px)";
                  e.target.style.boxShadow = "0 8px 15px rgba(0, 0, 0, 0.15)";
                  e.target.style.borderColor = isDynamic ? "#7C3AED" : "#3B82F6";
                }}
                onMouseOut={(e) => {
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
                  e.target.style.borderColor = isDynamic ? "#8B5CF6" : "#E5E7EB";
                }}
              >
                <span>{option}</span>
                {isDynamic && (
                  <span style={{
                    position: "absolute",
                    top: "8px",
                    right: "8px",
                    backgroundColor: "#8B5CF6",
                    color: "white",
                    fontSize: "0.75rem",
                    padding: "2px 6px",
                    borderRadius: "0.25rem",
                    fontWeight: "500"
                  }}>
                    Dynamic
                  </span>
                )}
                {isDynamic && optionData._description && (
                  <span style={{
                    fontSize: "0.75rem",
                    color: "#6B7280",
                    marginTop: "4px",
                    fontWeight: "400"
                  }}>
                    {optionData._description}
                  </span>
                )}
              </button>
            );
          })}
        </div>

        {/* Navigation Buttons */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        }}>
          <button
            onClick={goBack}
            style={{
              padding: "12px 24px",
              backgroundColor: "#6B7280",
              color: "white",
              border: "none",
              borderRadius: "0.75rem",
              fontSize: "1rem",
              fontWeight: "600",
              cursor: "pointer",
              transition: "all 0.2s ease-in-out"
            }}
            onMouseOver={(e) => e.target.style.backgroundColor = "#4B5563"}
            onMouseOut={(e) => e.target.style.backgroundColor = "#6B7280"}
          >
            {selectedPath.length === 0 ? "Back to Brand Selection" : "Back"}
          </button>

          <div style={{
            fontSize: "0.875rem",
            color: "#6B7280"
          }}>
            {currentOptions.length} option{currentOptions.length !== 1 ? 's' : ''} available
          </div>
        </div>
      </div>
    </div>
  );
}
