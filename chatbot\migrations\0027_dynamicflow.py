# Generated by Django 5.2.2 on 2025-10-18 01:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0026_alter_supportticket_operating_system_detailed'),
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicFlow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('flow_name', models.CharField(help_text="Human-readable name for the flow (e.g., 'Linea GigE Monochrome')", max_length=200, unique=True)),
                ('flow_path', models.JSONField(help_text="Hierarchical path array (e.g., ['Linea', 'GigE', 'Monochrome'])")),
                ('weaviate_class_name', models.CharField(help_text="Generated Weaviate class name (e.g., 'LineaGiGEMonochrome')", max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Description of what this flow represents')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this flow is active and available for selection')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this flow was created')),
                ('updated_at', models.DateTimeField(auto_now=True, help_text='When this flow was last updated')),
                ('weaviate_class_created', models.BooleanField(default=False, help_text='Whether the Weaviate class has been successfully created')),
                ('weaviate_class_error', models.TextField(blank=True, help_text='Error message if Weaviate class creation failed', null=True)),
                ('created_by', models.ForeignKey(blank=True, help_text='Admin user who created this flow', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'dynamic_flows',
                'ordering': ['flow_name'],
                'indexes': [models.Index(fields=['is_active'], name='dynamic_flo_is_acti_027b7c_idx'), models.Index(fields=['weaviate_class_name'], name='dynamic_flo_weaviat_acdaa3_idx'), models.Index(fields=['created_at'], name='dynamic_flo_created_598870_idx')],
            },
        ),
    ]
