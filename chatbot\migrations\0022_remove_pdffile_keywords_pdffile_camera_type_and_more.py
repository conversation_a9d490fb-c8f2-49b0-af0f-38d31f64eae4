# Generated by Django 5.2.2 on 2025-10-15 08:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('chatbot', '0021_remove_pdffile_keywords_pdffile_camera_type_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='pdffile',
            name='keywords',
        ),
        migrations.AddField(
            model_name='pdffile',
            name='camera_type',
            field=models.CharField(blank=True, choices=[('area_scan', 'Area Scan'), ('line_scan', 'Line Scan'), ('unknown', 'Unknown')], default='unknown', max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='color_type',
            field=models.CharField(blank=True, choices=[('Monochrome', 'Monochrome'), ('Color', 'Color')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='supportticket',
            name='interface_type',
            field=models.Char<PERSON>ield(blank=True, choices=[('GigE', 'GigE'), ('CameraLink', 'CameraLink')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='supportticket',
            name='brand',
            field=models.CharField(blank=True, choices=[('DALSA', 'DALSA'), ('FLIR', 'FLIR'), ('OTHERS', 'OTHERS')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='supportticket',
            name='family_name',
            field=models.CharField(blank=True, choices=[('Genie Nano (GigE)', 'Genie Nano (GigE)'), ('Blackfly S', 'Blackfly S'), ('Sapera GigE', 'Sapera GigE'), ('Other', 'Other')], max_length=100, null=True),
        ),
    ]
