import { useState, useEffect } from 'react';
import { BACKEND_URL } from '../utils/api';

/**
 * Custom hook to fetch and manage dynamic flows
 * Provides integration with product hierarchy and automatic updates
 */
export const useDynamicFlows = () => {
  const [flows, setFlows] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchFlows = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${BACKEND_URL}/api/available_flows/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access')}`,
        },
      });

      const data = await response.json();
      if (response.ok && data.success) {
        setFlows(data.flows || []);
      } else {
        setError(data.error || 'Failed to fetch flows');
        setFlows([]); // Set empty array on error
      }
    } catch (err) {
      setError('Network error: ' + err.message);
      setFlows([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFlows();
  }, []);

  /**
   * Integrate dynamic flows into existing product hierarchy
   * @param {Object} baseHierarchy - The base product hierarchy
   * @returns {Object} Enhanced hierarchy with dynamic flows
   */
  const enhanceHierarchyWithFlows = (baseHierarchy) => {
    if (!flows || !Array.isArray(flows) || flows.length === 0 || !baseHierarchy) {
      return baseHierarchy || {};
    }

    const enhanced = JSON.parse(JSON.stringify(baseHierarchy)); // Deep clone

    flows.forEach(flow => {
      if (!flow || !flow.flow_path || !Array.isArray(flow.flow_path) || flow.flow_path.length === 0) return;

      let currentLevel = enhanced;

      // Navigate through the hierarchy path
      for (let i = 0; i < flow.flow_path.length; i++) {
        const pathElement = flow.flow_path[i];

        if (!pathElement) continue; // Skip empty path elements

        if (i === flow.flow_path.length - 1) {
          // Last element - this is where we add the flow
          if (!currentLevel[pathElement]) {
            currentLevel[pathElement] = {
              _isDynamicFlow: true,
              _flowId: flow.id,
              _weaviateClass: flow.weaviate_class_name,
              _description: flow.description,
              models: [] // Placeholder for models
            };
          }
        } else {
          // Intermediate element - ensure path exists
          if (!currentLevel[pathElement]) {
            currentLevel[pathElement] = {};
          }
          currentLevel = currentLevel[pathElement];
        }
      }
    });

    return enhanced;
  };

  /**
   * Get flows that match a specific path
   * @param {Array} path - The path to match
   * @returns {Array} Matching flows
   */
  const getFlowsForPath = (path) => {
    if (!flows || !Array.isArray(flows) || !path || !Array.isArray(path)) return [];

    return flows.filter(flow => {
      if (!flow || !flow.flow_path || !Array.isArray(flow.flow_path)) return false;

      // Check if the flow path matches the given path
      if (flow.flow_path.length !== path.length) return false;

      return flow.flow_path.every((element, index) =>
        element && path[index] && element.toLowerCase() === path[index].toLowerCase()
      );
    });
  };

  /**
   * Get all available flow paths as options for dropdowns
   * @returns {Array} Array of flow options
   */
  const getFlowOptions = () => {
    if (!flows || !Array.isArray(flows)) return [];

    return flows.map(flow => ({
      id: flow?.id,
      label: flow?.flow_name || 'Unnamed Flow',
      value: flow?.flow_path || [],
      path: flow?.flow_path || [],
      weaviateClass: flow?.weaviate_class_name,
      description: flow?.description
    }));
  };

  /**
   * Check if a path represents a dynamic flow
   * @param {Array} path - The path to check
   * @returns {Object|null} Flow object if found, null otherwise
   */
  const isDynamicFlow = (path) => {
    if (!flows || !Array.isArray(flows) || !path || !Array.isArray(path)) return null;

    return flows.find(flow =>
      flow &&
      flow.flow_path &&
      Array.isArray(flow.flow_path) &&
      flow.flow_path.length === path.length &&
      flow.flow_path.every((element, index) =>
        element && path[index] && element.toLowerCase() === path[index].toLowerCase()
      )
    ) || null;
  };

  /**
   * Get Weaviate class name for a given path
   * @param {Array} path - The path to get class for
   * @returns {string|null} Weaviate class name or null
   */
  const getWeaviateClassForPath = (path) => {
    const flow = isDynamicFlow(path);
    return flow ? flow.weaviate_class_name : null;
  };

  return {
    flows,
    loading,
    error,
    refetch: fetchFlows,
    enhanceHierarchyWithFlows,
    getFlowsForPath,
    getFlowOptions,
    isDynamicFlow,
    getWeaviateClassForPath
  };
};

/**
 * Hook specifically for product selection components
 * Provides enhanced hierarchy with dynamic flows integrated
 */
export const useEnhancedProductHierarchy = () => {
  const [baseHierarchy, setBaseHierarchy] = useState({});
  const [enhancedHierarchy, setEnhancedHierarchy] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const { flows, loading: flowsLoading, enhanceHierarchyWithFlows } = useDynamicFlows();

  // Fetch base hierarchy
  useEffect(() => {
    const fetchHierarchy = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`${BACKEND_URL}/api/product_hierarchy/`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access')}`,
          },
        });

        const data = await response.json();
        if (response.ok) {
          setBaseHierarchy(data.hierarchy || {});
        } else {
          setError(data.message || 'Failed to load product hierarchy');
          setBaseHierarchy({}); // Set empty object on error
        }
      } catch (err) {
        setError('Network error: ' + err.message);
        setBaseHierarchy({}); // Set empty object on error
      } finally {
        setLoading(false);
      }
    };

    fetchHierarchy();
  }, []);

  // Enhance hierarchy when base hierarchy or flows change
  useEffect(() => {
    if (!flowsLoading) {
      const enhanced = enhanceHierarchyWithFlows(baseHierarchy);
      setEnhancedHierarchy(enhanced || {});
    }
  }, [baseHierarchy, flows, flowsLoading, enhanceHierarchyWithFlows]);

  return {
    hierarchy: enhancedHierarchy,
    baseHierarchy,
    flows,
    loading: loading || flowsLoading,
    error
  };
};

export default useDynamicFlows;
